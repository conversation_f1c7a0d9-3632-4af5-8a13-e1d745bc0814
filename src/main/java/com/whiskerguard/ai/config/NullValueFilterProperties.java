/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：NullValueFilterProperties.java
 * 包    名：com.whiskerguard.ai.config
 * 描    述：null值过滤配置属性类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/7/1
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.config;

import java.util.HashMap;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * null值过滤配置属性类
 * <p>
 * 用于配置AI服务返回结果中null值的替换策略，包括默认替换文本、
 * 字段级别的特定替换文本等配置项。
 *
 * 配置示例：
 * <pre>
 * whiskerguard:
 *   ai:
 *     null-value-filter:
 *       enabled: true
 *       default-replacement: "暂无意见"
 *       field-specific:
 *         riskSummary: "暂无风险总结"
 *         recommendations: "暂无建议"
 *         aiModelInfo: "模型信息不可用"
 * </pre>
 *
 * <AUTHOR> AI Team
 * @since 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "whiskerguard.ai.null-value-filter")
public class NullValueFilterProperties {

    /**
     * 是否启用null值过滤功能
     * 默认启用
     */
    private boolean enabled = true;

    /**
     * 默认替换文本
     * 当字段值为null时，使用此文本进行替换
     */
    private String defaultReplacement = "暂无意见";

    /**
     * 字段级别的特定替换文本映射
     * 可以为不同的字段配置不同的替换文本
     * 
     * 键：字段名
     * 值：替换文本
     */
    private Map<String, String> fieldSpecific = new HashMap<>();

    /**
     * 是否启用深度处理
     * 如果启用，会递归处理嵌套对象中的null值
     * 默认启用
     */
    private boolean enableDeepProcessing = true;

    /**
     * 是否启用集合处理
     * 如果启用，会处理集合中的null元素
     * 默认启用
     */
    private boolean enableCollectionProcessing = true;

    /**
     * 是否启用缓存
     * 如果启用，会缓存反射信息以提高性能
     * 默认启用
     */
    private boolean enableCache = true;

    /**
     * 最大处理深度
     * 防止循环引用导致的无限递归
     * 默认10层
     */
    private int maxProcessingDepth = 10;

    /**
     * 默认构造函数
     */
    public NullValueFilterProperties() {
        // 初始化默认的字段特定配置
        initializeDefaultFieldSpecific();
    }

    /**
     * 初始化默认的字段特定配置
     * <p>
     * 为常见的AI服务响应字段设置默认的友好提示文本。
     */
    private void initializeDefaultFieldSpecific() {
        fieldSpecific.put("riskSummary", "暂无风险总结");
        fieldSpecific.put("reviewStatus", "状态未知");
        fieldSpecific.put("aiModelInfo", "模型信息不可用");
        fieldSpecific.put("recommendations", "暂无建议");
        fieldSpecific.put("suggestions", "暂无建议");
        fieldSpecific.put("nextActions", "暂无后续行动");
        fieldSpecific.put("priorityActions", "暂无优先事项");
        fieldSpecific.put("monitoringSuggestions", "暂无监控建议");
        fieldSpecific.put("optimizationSuggestions", "暂无优化建议");
        fieldSpecific.put("executiveSummary", "暂无摘要");
        fieldSpecific.put("reviewSummary", "暂无审查总结");
        fieldSpecific.put("compliancePoints", "暂无合规要点");
        fieldSpecific.put("legalRisk", "暂无法律风险");
        fieldSpecific.put("description", "暂无描述");
        fieldSpecific.put("content", "暂无内容");
    }

    /**
     * 获取指定字段的替换文本
     * <p>
     * 优先使用字段特定配置，如果没有则使用默认替换文本。
     *
     * @param fieldName 字段名
     * @return 替换文本
     */
    public String getReplacementForField(String fieldName) {
        return fieldSpecific.getOrDefault(fieldName, defaultReplacement);
    }

    /**
     * 添加字段特定配置
     * <p>
     * 动态添加或更新字段的替换文本配置。
     *
     * @param fieldName 字段名
     * @param replacement 替换文本
     */
    public void addFieldSpecific(String fieldName, String replacement) {
        if (fieldName != null && replacement != null) {
            fieldSpecific.put(fieldName, replacement);
        }
    }

    /**
     * 移除字段特定配置
     * <p>
     * 移除指定字段的特定配置，该字段将使用默认替换文本。
     *
     * @param fieldName 字段名
     */
    public void removeFieldSpecific(String fieldName) {
        if (fieldName != null) {
            fieldSpecific.remove(fieldName);
        }
    }

    /**
     * 清空所有字段特定配置
     * <p>
     * 清空所有字段特定配置，所有字段将使用默认替换文本。
     */
    public void clearFieldSpecific() {
        fieldSpecific.clear();
    }

    // Getter and Setter methods

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDefaultReplacement() {
        return defaultReplacement;
    }

    public void setDefaultReplacement(String defaultReplacement) {
        this.defaultReplacement = defaultReplacement;
    }

    public Map<String, String> getFieldSpecific() {
        return fieldSpecific;
    }

    public void setFieldSpecific(Map<String, String> fieldSpecific) {
        this.fieldSpecific = fieldSpecific != null ? fieldSpecific : new HashMap<>();
    }

    public boolean isEnableDeepProcessing() {
        return enableDeepProcessing;
    }

    public void setEnableDeepProcessing(boolean enableDeepProcessing) {
        this.enableDeepProcessing = enableDeepProcessing;
    }

    public boolean isEnableCollectionProcessing() {
        return enableCollectionProcessing;
    }

    public void setEnableCollectionProcessing(boolean enableCollectionProcessing) {
        this.enableCollectionProcessing = enableCollectionProcessing;
    }

    public boolean isEnableCache() {
        return enableCache;
    }

    public void setEnableCache(boolean enableCache) {
        this.enableCache = enableCache;
    }

    public int getMaxProcessingDepth() {
        return maxProcessingDepth;
    }

    public void setMaxProcessingDepth(int maxProcessingDepth) {
        this.maxProcessingDepth = Math.max(1, maxProcessingDepth); // 至少为1
    }

    @Override
    public String toString() {
        return (
            "NullValueFilterProperties{" +
            "enabled=" +
            enabled +
            ", defaultReplacement='" +
            defaultReplacement +
            '\'' +
            ", fieldSpecificSize=" +
            fieldSpecific.size() +
            ", enableDeepProcessing=" +
            enableDeepProcessing +
            ", enableCollectionProcessing=" +
            enableCollectionProcessing +
            ", enableCache=" +
            enableCache +
            ", maxProcessingDepth=" +
            maxProcessingDepth +
            '}'
        );
    }
}
