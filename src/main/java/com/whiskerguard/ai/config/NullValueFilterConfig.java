/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：NullValueFilterConfig.java
 * 包    名：com.whiskerguard.ai.config
 * 描    述：null值过滤配置类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/7/1
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.config;

import com.whiskerguard.ai.util.NullValueFilterUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * null值过滤配置类
 * <p>
 * 负责配置和管理null值过滤相关的Bean和定时任务。
 * 提供null值过滤工具的Spring配置和缓存清理等功能。
 *
 * 主要功能：
 * 1. 配置null值过滤工具Bean
 * 2. 定时清理反射缓存
 * 3. 监控缓存使用情况
 * 4. 条件化启用功能
 *
 * <AUTHOR> AI Team
 * @since 1.0.0
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "whiskerguard.ai.null-value-filter", name = "enabled", havingValue = "true", matchIfMissing = true)
public class NullValueFilterConfig {

    private static final Logger log = LoggerFactory.getLogger(NullValueFilterConfig.class);

    /**
     * null值过滤配置属性
     */
    private final NullValueFilterProperties properties;

    /**
     * 构造函数
     *
     * @param properties null值过滤配置属性
     */
    public NullValueFilterConfig(NullValueFilterProperties properties) {
        this.properties = properties;
    }

    /**
     * 配置null值过滤工具Bean
     * <p>
     * 创建并配置null值过滤工具实例，供其他组件使用。
     *
     * @return null值过滤工具实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "whiskerguard.ai.null-value-filter", name = "enabled", havingValue = "true", matchIfMissing = true)
    public NullValueFilterUtil nullValueFilterUtil() {
        log.info("初始化null值过滤工具，配置: {}", properties);
        return new NullValueFilterUtil(properties);
    }

    /**
     * 定期清理反射缓存
     * <p>
     * 每2小时执行一次缓存清理，避免内存泄漏和过期数据积累。
     * 只有在启用缓存功能时才执行清理操作。
     */
    @Scheduled(fixedRate = 7200000) // 每2小时执行一次
    @ConditionalOnProperty(prefix = "whiskerguard.ai.null-value-filter", name = "enable-cache", havingValue = "true", matchIfMissing = true)
    public void cleanupReflectionCache() {
        if (!properties.isEnableCache()) {
            return;
        }

        try {
            log.debug("开始清理null值过滤工具反射缓存");
            
            // 获取清理前的缓存统计
            var statsBefore = NullValueFilterUtil.getCacheStatistics();
            log.debug("清理前缓存统计: {}", statsBefore);
            
            // 执行缓存清理
            NullValueFilterUtil.clearCache();
            
            log.debug("null值过滤工具反射缓存清理完成");
        } catch (Exception e) {
            log.error("清理null值过滤工具反射缓存时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 定期输出缓存统计信息（仅在DEBUG级别）
     * <p>
     * 每30分钟输出一次当前的缓存使用情况，
     * 帮助监控和调试缓存性能。
     */
    @Scheduled(fixedRate = 1800000) // 每30分钟执行一次
    @ConditionalOnProperty(prefix = "whiskerguard.ai.null-value-filter", name = "enable-cache", havingValue = "true", matchIfMissing = true)
    public void logCacheStatistics() {
        if (!properties.isEnableCache() || !log.isDebugEnabled()) {
            return;
        }

        try {
            var stats = NullValueFilterUtil.getCacheStatistics();
            log.debug("null值过滤工具缓存统计: {}", stats);
        } catch (Exception e) {
            log.warn("获取null值过滤工具缓存统计信息时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 应用启动时的配置验证和日志输出
     * <p>
     * 在Spring容器启动完成后，验证配置的有效性并输出相关信息。
     */
    @Scheduled(initialDelay = 5000, fixedDelay = Long.MAX_VALUE) // 启动5秒后执行一次
    public void validateAndLogConfiguration() {
        try {
            log.info("null值过滤功能已启用");
            log.info("默认替换文本: {}", properties.getDefaultReplacement());
            log.info("字段特定配置数量: {}", properties.getFieldSpecific().size());
            log.info("深度处理: {}", properties.isEnableDeepProcessing() ? "启用" : "禁用");
            log.info("集合处理: {}", properties.isEnableCollectionProcessing() ? "启用" : "禁用");
            log.info("缓存功能: {}", properties.isEnableCache() ? "启用" : "禁用");
            log.info("最大处理深度: {}", properties.getMaxProcessingDepth());

            // 验证配置的合理性
            validateConfiguration();
        } catch (Exception e) {
            log.error("验证null值过滤配置时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 验证配置的合理性
     * <p>
     * 检查配置项的有效性，如果发现问题会记录警告日志。
     */
    private void validateConfiguration() {
        // 验证默认替换文本
        if (properties.getDefaultReplacement() == null || properties.getDefaultReplacement().trim().isEmpty()) {
            log.warn("默认替换文本为空，可能导致null值未被正确替换");
        }

        // 验证最大处理深度
        if (properties.getMaxProcessingDepth() < 1) {
            log.warn("最大处理深度设置过小 ({}), 可能导致嵌套对象处理不完整", properties.getMaxProcessingDepth());
        } else if (properties.getMaxProcessingDepth() > 50) {
            log.warn("最大处理深度设置过大 ({}), 可能导致性能问题", properties.getMaxProcessingDepth());
        }

        // 验证字段特定配置
        if (properties.getFieldSpecific() != null) {
            properties.getFieldSpecific().forEach((field, replacement) -> {
                if (replacement == null || replacement.trim().isEmpty()) {
                    log.warn("字段 '{}' 的替换文本为空", field);
                }
            });
        }

        log.debug("null值过滤配置验证完成");
    }
}
