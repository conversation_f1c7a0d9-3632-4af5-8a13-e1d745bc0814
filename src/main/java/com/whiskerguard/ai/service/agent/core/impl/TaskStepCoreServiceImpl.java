package com.whiskerguard.ai.service.agent.core.impl;

import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.repository.TaskStepRepository;
import com.whiskerguard.ai.security.SecurityUtils;
import com.whiskerguard.ai.service.agent.core.TaskStepCoreService;
import jakarta.persistence.EntityNotFoundException;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 任务步骤核心服务实现
 * Task Step Core Service Implementation
 *
 * 提供Agent任务步骤的管理和执行功能实现
 * Provide implementation of management and execution functions for Agent task steps
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class TaskStepCoreServiceImpl implements TaskStepCoreService {

    private static final Logger log = LoggerFactory.getLogger(TaskStepCoreServiceImpl.class);

    private final TaskStepRepository taskStepRepository;
    private final AgentTaskRepository agentTaskRepository;

    /**
     * 构造函数注入依赖
     * Constructor dependency injection
     */
    public TaskStepCoreServiceImpl(TaskStepRepository taskStepRepository, AgentTaskRepository agentTaskRepository) {
        this.taskStepRepository = taskStepRepository;
        this.agentTaskRepository = agentTaskRepository;
    }

    @Override
    public TaskStep createStep(Long agentTaskId, String stepName, String stepType, Integer stepOrder) {
        return createStep(agentTaskId, stepName, null, stepType, stepOrder);
    }

    @Override
    public TaskStep createStep(Long agentTaskId, String stepName, String stepDescription, String stepType, Integer stepOrder) {
        log.debug(
            "创建任务步骤 - 任务ID: {}, 步骤名称: {}, 类型: {}, 顺序: {} / Creating task step - Task ID: {}, Step name: {}, Type: {}, Order: {}",
            agentTaskId,
            stepName,
            stepType,
            stepOrder,
            agentTaskId,
            stepName,
            stepType,
            stepOrder
        );

        // 获取关联的AgentTask / Get associated AgentTask
        AgentTask agentTask = agentTaskRepository
            .findById(agentTaskId)
            .orElseThrow(() -> {
                log.error("未找到Agent任务 - ID: {} / Agent task not found - ID: {}", agentTaskId, agentTaskId);
                return new EntityNotFoundException("Agent任务不存在 - ID: " + agentTaskId + " / Agent task not found - ID: " + agentTaskId);
            });

        // 创建新的步骤实体 / Create new step entity
        TaskStep step = new TaskStep();
        step.setAgentTask(agentTask); // 设置任务关联
        step.setStepName(stepName);
        step.setStepDescription(stepDescription);
        step.setStepType(stepType);
        step.setStepOrder(stepOrder);
        step.setStatus(TaskStepStatus.PENDING.getValue()); // 使用String值而不是枚举
        step.setRetryCount(0);
        step.setVersion(1);
        step.setIsDeleted(false);

        // 设置tenantId，如果AgentTask的tenantId为null，则使用默认值1
        Long tenantId = agentTask.getTenantId() != null ? agentTask.getTenantId() : 1L;
        step.setTenantId(tenantId);

        // 设置审计字段 / Set audit fields
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse("system");
        Instant now = Instant.now();
        step.setCreatedBy(currentUser);
        step.setCreatedAt(now);
        step.setUpdatedBy(currentUser);
        step.setUpdatedAt(now);

        // 保存并返回 / Save and return
        TaskStep savedStep = taskStepRepository.save(step);
        log.info(
            "成功创建任务步骤 - ID: {}, 任务ID: {}, 步骤名称: {} / Successfully created task step - ID: {}, Task ID: {}, Step name: {}",
            savedStep.getId(),
            agentTaskId,
            stepName,
            savedStep.getId(),
            agentTaskId,
            stepName
        );

        return savedStep;
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void updateStepStatus(Long stepId, String status) {
        log.debug(
            "更新步骤状态 - 步骤ID: {}, 新状态: {} / Updating step status - Step ID: {}, New status: {}",
            stepId,
            status,
            stepId,
            status
        );

        TaskStep step = getStepById(stepId);
        String oldStatus = step.getStatus();
        step.setStatus(status);

        // 如果状态改为RUNNING，记录开始时间 / If status changes to RUNNING, record start time
        if (TaskStepStatus.RUNNING.getValue().equals(status) && step.getStartTime() == null) {
            step.setStartTime(Instant.now());
        }

        // 如果状态改为完成状态，记录结束时间和执行时长 / If status changes to completion status, record end time and execution time
        if (isCompletionStatus(status) && step.getEndTime() == null) {
            Instant endTime = Instant.now();
            step.setEndTime(endTime);

            if (step.getStartTime() != null) {
                long executionTime = Duration.between(step.getStartTime(), endTime).toMillis();
                step.setExecutionTime(executionTime);
            }
        }

        updateAuditFields(step);
        taskStepRepository.save(step);

        log.info(
            "步骤状态已更新 - 步骤ID: {}, 从 {} 变为 {} / Step status updated - Step ID: {}, from {} to {}",
            stepId,
            oldStatus,
            status,
            stepId,
            oldStatus,
            status
        );
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#step.id")
    public TaskStep updateStep(TaskStep step) {
        log.debug("更新步骤实体 - 步骤ID: {} / Updating step entity - Step ID: {}", step.getId(), step.getId());

        updateAuditFields(step);
        TaskStep updatedStep = taskStepRepository.save(step);

        log.debug("步骤实体更新完成 - 步骤ID: {} / Step entity updated - Step ID: {}", step.getId(), step.getId());
        return updatedStep;
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void recordInput(Long stepId, String inputData) {
        log.debug("记录步骤输入 - 步骤ID: {} / Recording step input - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        step.setInputData(inputData);
        updateAuditFields(step);
        taskStepRepository.save(step);
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void recordOutput(Long stepId, String outputData) {
        log.debug("记录步骤输出 - 步骤ID: {} / Recording step output - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        step.setOutputData(outputData);
        updateAuditFields(step);
        taskStepRepository.save(step);
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void recordError(Long stepId, String errorMessage) {
        log.debug("记录步骤错误 - 步骤ID: {} / Recording step error - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        step.setErrorMessage(errorMessage);
        updateAuditFields(step);
        taskStepRepository.save(step);
    }

    @Override
    public void startStep(Long stepId) {
        log.debug("开始执行步骤 - 步骤ID: {} / Starting step execution - Step ID: {}", stepId, stepId);
        updateStepStatus(stepId, TaskStepStatus.RUNNING.getValue());
    }

    @Override
    public void completeStep(Long stepId, String outputData) {
        log.debug("完成步骤执行 - 步骤ID: {} / Completing step execution - Step ID: {}", stepId, stepId);

        if (outputData != null) {
            recordOutput(stepId, outputData);
        }
        updateStepStatus(stepId, TaskStepStatus.COMPLETED.getValue());
    }

    @Override
    public void failStep(Long stepId, String errorMessage) {
        log.debug("步骤执行失败 - 步骤ID: {} / Step execution failed - Step ID: {}", stepId, stepId);

        if (errorMessage != null) {
            recordError(stepId, errorMessage);
        }
        updateStepStatus(stepId, TaskStepStatus.FAILED.getValue());
    }

    @Override
    public void skipStep(Long stepId, String reason) {
        log.debug(
            "跳过步骤执行 - 步骤ID: {}, 原因: {} / Skipping step execution - Step ID: {}, Reason: {}",
            stepId,
            reason,
            stepId,
            reason
        );

        if (reason != null) {
            // 将跳过原因记录在错误信息字段中 / Record skip reason in error message field
            recordError(stepId, "步骤被跳过: " + reason + " / Step skipped: " + reason);
        }
        updateStepStatus(stepId, TaskStepStatus.SKIPPED.getValue());
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public boolean retryStep(Long stepId) {
        log.debug("重试步骤执行 - 步骤ID: {} / Retrying step execution - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);

        // 检查是否可以重试 / Check if can retry
        if (step.getStatus() != TaskStepStatus.FAILED.getValue()) {
            log.warn(
                "只有失败的步骤可以重试 - 步骤ID: {}, 当前状态: {} / Only failed steps can be retried - Step ID: {}, Current status: {}",
                stepId,
                step.getStatus(),
                stepId,
                step.getStatus()
            );
            return false;
        }

        // 增加重试次数 / Increment retry count
        Integer retryCount = step.getRetryCount() != null ? step.getRetryCount() + 1 : 1;
        step.setRetryCount(retryCount);

        // 清除之前的错误信息和结束时间 / Clear previous error message and end time
        step.setErrorMessage(null);
        step.setEndTime(null);
        step.setExecutionTime(null);

        // 重置状态为PENDING / Reset status to PENDING
        step.setStatus(TaskStepStatus.PENDING.getValue());

        updateAuditFields(step);
        taskStepRepository.save(step);

        log.info(
            "步骤重试已设置 - 步骤ID: {}, 重试次数: {} / Step retry set - Step ID: {}, Retry count: {}",
            stepId,
            retryCount,
            stepId,
            retryCount
        );

        return true;
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "task-steps", key = "#agentTaskId")
    public List<TaskStep> getTaskSteps(Long agentTaskId) {
        log.debug("获取任务步骤 - 任务ID: {} / Getting task steps - Task ID: {}", agentTaskId, agentTaskId);
        return taskStepRepository.findByAgentTaskIdAndIsDeletedFalse(agentTaskId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getTaskStepsByTaskId(Long taskId) {
        log.debug("根据任务ID获取任务步骤 - 任务ID: {} / Getting task steps by task ID - Task ID: {}", taskId, taskId);
        return taskStepRepository.findByAgentTaskIdOrderByStepOrder(taskId);
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#taskStep.id")
    public TaskStep saveTaskStep(TaskStep taskStep) {
        log.debug("保存任务步骤 - 步骤ID: {} / Saving task step - Step ID: {}",
                taskStep.getId(), taskStep.getId());

        updateAuditFields(taskStep);
        TaskStep savedStep = taskStepRepository.save(taskStep);

        log.debug("任务步骤保存完成 - 步骤ID: {} / Task step saved - Step ID: {}",
                savedStep.getId(), savedStep.getId());
        return savedStep;
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "task-step", key = "#stepId")
    public TaskStep getStep(Long stepId) {
        log.debug("获取单个步骤 - 步骤ID: {} / Getting single step - Step ID: {}", stepId, stepId);
        return getStepById(stepId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getStepsByTask(Long agentTaskId) {
        return getTaskSteps(agentTaskId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getStepsByTaskId(Long agentTaskId) {
        return getTaskStepsByTaskId(agentTaskId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getStepsByStatusAndTask(Long agentTaskId, String status) {
        log.debug("根据任务ID和状态获取步骤 - 任务ID: {}, 状态: {} / Getting steps by task ID and status - Task ID: {}, Status: {}",
                agentTaskId, status, agentTaskId, status);
        return taskStepRepository.findByAgentTaskIdAndStatusAndIsDeletedFalse(agentTaskId, status);
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void setInput(Long stepId, String inputData) {
        recordInput(stepId, inputData);
    }

    @Override
    public void updateStatus(Long stepId, String status) {
        updateStepStatus(stepId, status);
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void incrementRetryCount(Long stepId) {
        log.debug("增加重试计数 - 步骤ID: {} / Incrementing retry count - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        Integer currentRetryCount = step.getRetryCount() != null ? step.getRetryCount() : 0;
        step.setRetryCount(currentRetryCount + 1);

        updateAuditFields(step);
        taskStepRepository.save(step);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getTaskStepsOrdered(Long agentTaskId) {
        log.debug("获取任务的步骤（按顺序） - 任务ID: {} / Getting task steps (ordered) - Task ID: {}",
                agentTaskId, agentTaskId);
        return taskStepRepository.findByAgentTaskIdAndIsDeletedFalseOrderByStepOrder(agentTaskId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getStepsByStatus(Long agentTaskId, String status) {
        return getStepsByStatusAndTask(agentTaskId, status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TaskStep> getExecutableSteps(Long agentTaskId) {
        log.debug("获取可执行的步骤 - 任务ID: {} / Getting executable steps - Task ID: {}",
                agentTaskId, agentTaskId);

        // 获取所有PENDING状态的步骤，这里简化实现，实际可能需要更复杂的依赖检查
        return getStepsByStatus(agentTaskId, TaskStepStatus.PENDING.getValue());
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canExecuteStep(Long stepId) {
        log.debug("检查步骤是否可以执行 - 步骤ID: {} / Checking if step can be executed - Step ID: {}",
                stepId, stepId);

        TaskStep step = getStepById(stepId);
        // 简化实现：只有PENDING状态的步骤可以执行
        return TaskStepStatus.PENDING.getValue().equals(step.getStatus());
    }

    @Override
    @CacheEvict(value = "task-steps", key = "#stepId")
    public void updateStepMetadata(Long stepId, String metadata) {
        log.debug("更新步骤元数据 - 步骤ID: {} / Updating step metadata - Step ID: {}", stepId, stepId);

        TaskStep step = getStepById(stepId);
        step.setMetadata(metadata);

        updateAuditFields(step);
        taskStepRepository.save(step);
    }

    @Override
    @Transactional(readOnly = true)
    public int getTaskProgress(Long agentTaskId) {
        log.debug("获取任务执行进度 - 任务ID: {} / Getting task execution progress - Task ID: {}",
                agentTaskId, agentTaskId);

        List<TaskStep> allSteps = getTaskSteps(agentTaskId);
        if (allSteps.isEmpty()) {
            return 0;
        }

        long completedSteps = allSteps.stream()
                .mapToLong(step ->
                    TaskStepStatus.COMPLETED.getValue().equals(step.getStatus()) ||
                    TaskStepStatus.SKIPPED.getValue().equals(step.getStatus()) ? 1 : 0)
                .sum();

        return (int) ((completedSteps * 100) / allSteps.size());
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getTaskStepStatistics(Long agentTaskId) {
        log.debug("获取步骤执行统计信息 - 任务ID: {} / Getting step execution statistics - Task ID: {}",
                agentTaskId, agentTaskId);

        List<TaskStep> allSteps = getTaskSteps(agentTaskId);

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalSteps", allSteps.size());
        statistics.put("pendingSteps", allSteps.stream().mapToLong(s ->
            TaskStepStatus.PENDING.getValue().equals(s.getStatus()) ? 1 : 0).sum());
        statistics.put("runningSteps", allSteps.stream().mapToLong(s ->
            TaskStepStatus.RUNNING.getValue().equals(s.getStatus()) ? 1 : 0).sum());
        statistics.put("completedSteps", allSteps.stream().mapToLong(s ->
            TaskStepStatus.COMPLETED.getValue().equals(s.getStatus()) ? 1 : 0).sum());
        statistics.put("failedSteps", allSteps.stream().mapToLong(s ->
            TaskStepStatus.FAILED.getValue().equals(s.getStatus()) ? 1 : 0).sum());
        statistics.put("skippedSteps", allSteps.stream().mapToLong(s ->
            TaskStepStatus.SKIPPED.getValue().equals(s.getStatus()) ? 1 : 0).sum());

        statistics.put("progress", getTaskProgress(agentTaskId));

        return statistics;
    }

    // ========== 私有辅助方法 / Private Helper Methods ==========

    /**
     * 根据ID获取步骤实体
     * Get step entity by ID
     */
    private TaskStep getStepById(Long stepId) {
        return taskStepRepository
            .findById(stepId)
            .orElseThrow(() -> {
                log.error("未找到任务步骤 - ID: {} / Task step not found - ID: {}", stepId, stepId);
                return new EntityNotFoundException("任务步骤不存在 - ID: " + stepId + " / Task step not found - ID: " + stepId);
            });
    }

    /**
     * 检查是否为完成状态
     * Check if it's a completion status
     */
    private boolean isCompletionStatus(String status) {
        return TaskStepStatus.COMPLETED.getValue().equals(status) ||
               TaskStepStatus.FAILED.getValue().equals(status) ||
               TaskStepStatus.SKIPPED.getValue().equals(status);
    }

    /**
     * 更新审计字段
     * Update audit fields
     */
    private void updateAuditFields(TaskStep step) {
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse("system");
        step.setUpdatedBy(currentUser);
        step.setUpdatedAt(Instant.now());
    }
}
