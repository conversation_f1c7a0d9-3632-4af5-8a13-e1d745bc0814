/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ComplianceAgentService.java
 * 包    名：com.whiskerguard.ai.service.agent
 * 描    述：合规智能体核心服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.security.SecurityUtils;
import com.whiskerguard.ai.service.agent.business.ContractReviewAgentService;
import com.whiskerguard.ai.service.agent.business.PolicyReviewAgentService;
import com.whiskerguard.ai.service.agent.business.RegulationInternalizationAgentService;
import com.whiskerguard.ai.service.agent.core.AgentContextCoreService;
import com.whiskerguard.ai.service.agent.core.TaskOrchestratorService;
import com.whiskerguard.ai.service.agent.core.TaskOrchestratorServiceInterface;
import com.whiskerguard.ai.service.agent.core.TaskStepCoreService;
import com.whiskerguard.ai.service.agent.dto.AgentTaskRequestDTO;
import com.whiskerguard.ai.service.agent.dto.AgentTaskResponseDTO;
import com.whiskerguard.ai.service.dto.ContractReviewRequestDTO;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import com.whiskerguard.ai.service.dto.PolicyReviewRequestDTO;
import com.whiskerguard.ai.service.dto.PolicyReviewResponseDTO;
import com.whiskerguard.ai.service.dto.RegulationInternalizationRequestDTO;
import com.whiskerguard.ai.service.dto.RegulationInternalizationResponseDTO;
import com.whiskerguard.ai.service.mapper.AgentTaskMapper;
import jakarta.validation.Valid;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合规智能体核心服务
 * <p>
 * 提供统一的Agent服务入口，协调各个业务Agent的执行。
 * 负责任务创建、状态管理、结果整合等核心功能。
 *
 * 主要功能：
 * 1. 任务创建和管理
 * 2. 业务Agent调度
 * 3. 执行状态跟踪
 * 4. 结果整合返回
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class ComplianceAgentService {

    private static final Logger log = LoggerFactory.getLogger(ComplianceAgentService.class);

    private final AgentTaskRepository agentTaskRepository;
    private final AgentTaskMapper agentTaskMapper;
    private final TaskOrchestratorService taskOrchestratorService;
    private final TaskStepCoreService taskStepCoreService;
    private final AgentContextCoreService agentContextCoreService;
    private final RegulationInternalizationAgentService regulationInternalizationService;
    private final PolicyReviewAgentService policyReviewService;
    private final ContractReviewAgentService contractReviewService;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public ComplianceAgentService(
        AgentTaskRepository agentTaskRepository,
        AgentTaskMapper agentTaskMapper,
        TaskOrchestratorService taskOrchestratorService,
        TaskStepCoreService taskStepCoreService,
        AgentContextCoreService agentContextCoreService,
        RegulationInternalizationAgentService regulationInternalizationService,
        PolicyReviewAgentService policyReviewService,
        ContractReviewAgentService contractReviewService
    ) {
        this.agentTaskRepository = agentTaskRepository;
        this.agentTaskMapper = agentTaskMapper;
        this.taskOrchestratorService = taskOrchestratorService;
        this.taskStepCoreService = taskStepCoreService;
        this.agentContextCoreService = agentContextCoreService;
        this.regulationInternalizationService = regulationInternalizationService;
        this.policyReviewService = policyReviewService;
        this.contractReviewService = contractReviewService;
    }

    /**
     * 创建Agent任务
     *
     * @param request 任务请求
     * @return 任务响应
     */
    public AgentTaskResponseDTO createTask(@Valid AgentTaskRequestDTO request) {
        log.info("创建Agent任务，类型: {}, 租户: {}", request.getTaskType(), request.getTenantId());

        try {
            // 1. 创建任务实体
            AgentTask agentTask = createAgentTaskEntity(request);
            final AgentTask savedTask = agentTaskRepository.save(agentTask);

            // 2. 返回任务响应
            AgentTaskResponseDTO response = buildTaskResponse(savedTask, null);

            // 3. 异步执行任务 - 添加短暂延迟确保事务提交
            CompletableFuture.runAsync(() -> {
                try {
                    // 短暂延迟确保主事务已提交
                    Thread.sleep(100);

                    // 重新从数据库获取任务实体，确保获取到已提交的数据
                    Optional<AgentTask> taskOpt = agentTaskRepository.findById(savedTask.getId());
                    if (taskOpt.isPresent()) {
                        executeTaskAsync(taskOpt.orElseThrow());
                    } else {
                        log.error("异步任务启动失败，任务不存在，ID: {}", savedTask.getId());
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("异步任务启动被中断，任务ID: {}", savedTask.getId());
                } catch (Exception e) {
                    log.error("执行异步任务失败，任务ID: {}, 错误: {}", savedTask.getId(), e.getMessage(), e);
                }
            });

            return response;
        } catch (Exception e) {
            log.error("创建Agent任务失败", e);
            throw new RuntimeException("创建Agent任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    @Transactional(readOnly = true)
    public AgentTaskResponseDTO getTaskStatus(Long taskId) {
        log.debug("获取任务状态，任务ID: {}", taskId);

        Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        AgentTask task = taskOpt.orElseThrow(); // Fixed: replaced get() with orElseThrow()
        return buildTaskResponse(task, task.getResponseData());
    }

    /**
     * 获取任务结果
     *
     * @param taskId 任务ID
     * @return 任务结果
     */
    @Transactional(readOnly = true)
    public AgentTaskResponseDTO getTaskResult(Long taskId) {
        log.debug("获取任务结果，任务ID: {}", taskId);

        Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        AgentTask task = taskOpt.orElseThrow(); // Fixed: replaced get() with orElseThrow()
        return buildTaskResponse(task, task.getResponseData());
    }

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     */
    public void cancelTask(Long taskId) {
        log.info("取消任务，任务ID: {}", taskId);

        Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        AgentTask task = taskOpt.orElseThrow();
        if ("RUNNING".equals(task.getStatus())) {
            task.setStatus("CANCELLED");
            task.setEndTime(Instant.now());
            task.setUpdatedAt(Instant.now());
            task.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
            agentTaskRepository.save(task);
        }
    }

    /**
     * 创建Agent任务实体
     */
    private AgentTask createAgentTaskEntity(AgentTaskRequestDTO request) {
        AgentTask agentTask = new AgentTask();
        agentTask.setTenantId(request.getTenantId());
        agentTask.setTaskType(request.getTaskType());
        agentTask.setTitle(request.getTitle());
        agentTask.setDescription(request.getDescription());
        agentTask.setStatus("PENDING");
        // 确保 priority 字段使用字符串类��
        String priority = request.getPriority();
        agentTask.setPriority(priority != null ? priority : "NORMAL");
        agentTask.setRequestData(request.getRequestData());
        agentTask.setProgress(0);
        agentTask.setVersion(1);
        agentTask.setCreatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
        agentTask.setCreatedAt(Instant.now());
        agentTask.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
        agentTask.setUpdatedAt(Instant.now());
        agentTask.setIsDeleted(false);
        return agentTask;
    }

    /**
     * 异步执行任务
     */
    @org.springframework.scheduling.annotation.Async
    public void executeTaskAsync(AgentTask agentTask) {
        try {
            log.info("开始执行Agent任务，ID: {}, 类型: {}", agentTask.getId(), agentTask.getTaskType());

            // 更新任务状态为运行中
            updateTaskStatus(agentTask, "RUNNING", 10);

            // 1. 创建任务步骤 - 使用TaskOrchestratorService创建标准化步骤
            createTaskSteps(agentTask);

            // 2. 创建任务上下文 - 为任务执行提供上下文环境
            createTaskContext(agentTask);

            // 3. 开始执行步骤并更新TaskStep状态
            updateTaskStepsStatus(agentTask.getId(), TaskStepStatus.RUNNING);

            // 4. 根据任务类型调用相应的业务Agent
            String result =
                switch (agentTask.getTaskType()) {
                    case "REGULATION_INTERNALIZATION" -> executeRegulationInternalization(agentTask);
                    case "POLICY_REVIEW" -> executePolicyReview(agentTask);
                    case "CONTRACT_REVIEW" -> executeContractReview(agentTask);
                    default -> throw new IllegalArgumentException("不支持的任务类型: " + agentTask.getTaskType());
                };

            // 5. 更新任务状态为完成，并更新所有TaskStep为完成状态
            updateTaskCompletion(agentTask, "COMPLETED", result);
            updateTaskStepsStatus(agentTask.getId(), TaskStepStatus.COMPLETED);

            log.info("Agent任务执行完成，ID: {}", agentTask.getId());
        } catch (Exception e) {
            log.error("Agent任务执行失败，ID: {}", agentTask.getId(), e);

            // 6. 更新任务状态为失败，并更新所有TaskStep为失败状态
            updateTaskCompletion(agentTask, "FAILED", null);
            updateTaskError(agentTask, e.getMessage());
            updateTaskStepsStatus(agentTask.getId(), TaskStepStatus.FAILED);
        }
    }

    /**
     * 执行外规内化任务
     */
    private String executeRegulationInternalization(AgentTask agentTask) {
        log.debug("执行外规内化任务，ID: {}", agentTask.getId());

        RegulationInternalizationRequestDTO request = parseRequestData(
            agentTask.getRequestData(),
            RegulationInternalizationRequestDTO.class,
            agentTask.getTenantId(),
            0L
        ); //这里是硬编码，先做测试，后期来改为正式的 employeeId（userId)

        // 传递 AgentTask 参数给业务服务
        RegulationInternalizationResponseDTO response = regulationInternalizationService.processInternalization(request, agentTask);

        return convertToJson(response);
    }

    /**
     * 执行制度审查任务
     */
    private String executePolicyReview(AgentTask agentTask) {
        log.debug("执行制度审查任务，ID: {}", agentTask.getId());

        PolicyReviewRequestDTO request = parseRequestData(agentTask.getRequestData(), PolicyReviewRequestDTO.class);

        PolicyReviewResponseDTO response = policyReviewService.processPolicyReview(request, agentTask.getId());

        return convertToJson(response);
    }

    /**
     * 执行合同审查任务
     */
    private String executeContractReview(AgentTask agentTask) {
        log.debug("执行合同审查任务，ID: {}", agentTask.getId());

        ContractReviewRequestDTO request = parseRequestData(agentTask.getRequestData(), ContractReviewRequestDTO.class);

        ContractReviewResponseDTO response = contractReviewService.reviewContract(request, agentTask.getId());

        return convertToJson(response);
    }

    /**
     * 更新任务状态
     */
    public void updateTaskStatus(AgentTask agentTask, String status, Integer progress) {
        // 从数据库重新获取实体以确保正确的 Hibernate Session
        AgentTask taskToUpdate = agentTaskRepository
            .findById(agentTask.getId())
            .orElseThrow(() -> new RuntimeException("任务不存在，ID: " + agentTask.getId()));

        // 更新状态
        taskToUpdate.setStatus(status);
        if (progress != null) {
            taskToUpdate.setProgress(progress);
        }
        if ("RUNNING".equals(status) && taskToUpdate.getStartTime() == null) {
            taskToUpdate.setStartTime(Instant.now());
        }

        updateCommonFields(taskToUpdate);
        agentTaskRepository.save(taskToUpdate);

        log.debug("任务状态更新成功，ID: {}, 状态: {}, 进度: {}", agentTask.getId(), status, progress);
    }

    /**
     * 更新任务完成状态
     */
    public void updateTaskCompletion(AgentTask agentTask, String status, String result) {
        // 从数据库重新获取实体以确保正确的 Hibernate Session
        AgentTask taskToUpdate = agentTaskRepository
            .findById(agentTask.getId())
            .orElseThrow(() -> new RuntimeException("任务��存在，ID: " + agentTask.getId()));

        // 更新状态
        taskToUpdate.setStatus(status);
        taskToUpdate.setProgress(100);
        taskToUpdate.setEndTime(Instant.now());
        if (result != null) {
            taskToUpdate.setResponseData(result);
        }
        if (taskToUpdate.getStartTime() != null) {
            taskToUpdate.setExecutionTime(taskToUpdate.getEndTime().toEpochMilli() - taskToUpdate.getStartTime().toEpochMilli());
        }

        updateCommonFields(taskToUpdate);
        agentTaskRepository.save(taskToUpdate);

        log.debug("任务完成状态更新成��，ID: {}, 状���: {}", agentTask.getId(), status);
    }

    /**
     * 更新任务错误信息
     */
    public void updateTaskError(AgentTask agentTask, String errorMessage) {
        // 从数据库重新获取实体以确保正确的 Hibernate Session
        AgentTask taskToUpdate = agentTaskRepository
            .findById(agentTask.getId())
            .orElseThrow(() -> new RuntimeException("任务不存在，ID: " + agentTask.getId()));

        // 更新错误信息
        taskToUpdate.setErrorMessage(errorMessage);
        updateCommonFields(taskToUpdate);
        agentTaskRepository.save(taskToUpdate);

        log.debug("任务错误信息更新成功，ID: {}", agentTask.getId());
    }

    /**
     * 更新通用字段
     */
    private void updateCommonFields(AgentTask task) {
        task.setUpdatedAt(Instant.now());
        task.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
    }

    /**
     * 解析请求数据
     */
    private <T> T parseRequestData(String requestData, Class<T> valueType) {
        return parseRequestData(requestData, valueType, null, null);
    }

    private <T> T parseRequestData(String requestData, Class<T> valueType, Long tenantId, Long defaultEmployeeId) {
        if (requestData == null || requestData.isEmpty()) {
            return null;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.registerModule(new JavaTimeModule());
            T result = objectMapper.readValue(requestData, valueType);

            // 特殊处理：为合同审查请求添加默认的合同内容（如果缺失）
            if (result instanceof com.whiskerguard.ai.service.dto.ContractReviewRequestDTO) {
                com.whiskerguard.ai.service.dto.ContractReviewRequestDTO contractRequest =
                    (com.whiskerguard.ai.service.dto.ContractReviewRequestDTO) result;
                if (contractRequest.getContractContent() == null || contractRequest.getContractContent().trim().isEmpty()) {
                    log.warn("合同内容为空，使用默认测试内容，租户ID: {}", tenantId);
                    contractRequest.setContractContent(getDefaultContractContent());
                }
            }

            return result;
        } catch (Exception e) {
            log.error("解析请求数据失败，租户ID: {}, 数据: {}", tenantId, requestData, e);
            throw new RuntimeException("解析请求数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取默认的合同内容（用于测试）
     */
    private String getDefaultContractContent() {
        return """
        合同编号：TEST-CONTRACT-001

        甲方：测试公司A
        地址：北京市朝阳区测试街道123号
        法定代表人：张三

        乙方：测试公司B
        地址：上海市浦东新区测试路456号
        法定代表人：李四

        根据《中华人民共和国合同法》及相关法律法规，甲乙双方本着平等、自愿、公平、诚实信用的原则，
        就以下事项达成一致，签订本合同：

        第一条 合同标的
        甲方向乙方提供测试服务，包括但不限于技术咨询、系统开发等。

        第二条 合同金额
        本合同总金额为人民币100万元整（¥1,000,000.00）。

        第三条 履行期限
        合同履行期限为2024年1月1日至2024年12月31日。

        第四条 违约责任
        任何一方违反本合同约定，应承担相应的违约责任。

        第五条 争议解决
        因本合同引起的争议，双方应友好协商解决；协商不成的，
        提交北京仲裁委员会仲裁。

        第六条 其他
        本合同一式两份，甲乙双方各执一份，具有同等法律效力。

        甲方（盖章）：                    乙方（盖章）：

        日期：2024年1月1日                日期：2024年1月1日
        """;
    }

    /**
     * 转换为JSON格式
     */
    private String convertToJson(Object data) {
        if (data == null) {
            return null;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            objectMapper.registerModule(new JavaTimeModule());
            return objectMapper.writeValueAsString(data);
        } catch (Exception e) {
            log.error("转换为JSON格式失败，数据: {}", data, e);
            throw new RuntimeException("转换为JSON格式失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建任务响应
     */
    private AgentTaskResponseDTO buildTaskResponse(AgentTask task, String responseData) {
        // 使用Builder模式创建响应对象，而不是通过mapper转换
        AgentTaskResponseDTO response = AgentTaskResponseDTO.builder()
            .taskId(task.getId())
            .taskType(task.getTaskType())
            .title(task.getTitle())
            .status(task.getStatus())
            .progress(task.getProgress())
            .result(responseData) // 直接设置响应数据
            .startTime(task.getStartTime())
            .endTime(task.getEndTime())
            .executionTime(task.getExecutionTime())
            .errorMessage(task.getErrorMessage())
            .createdAt(task.getCreatedAt())
            .build();

        // 不尝试解析和设置特定类型的响应对象，因为AgentTaskResponseDTO可能没有这些setter方法
        // 客户端可以根据taskType和responseData自行解析特定类型的响应

        return response;
    }

    /**
     * 创建任务步骤
     * <p>
     * 根据任务类型创建标准化的执行步骤，为任务执行提供详细的步骤跟踪。
     * 这是Agent模块的核心功能之一，确保每个任务的执行过程都有完整的步骤记录。
     *
     * @param agentTask Agent任务实体
     */
    private void createTaskSteps(AgentTask agentTask) {
        try {
            log.info("为任务创建执行步骤，任务ID: {}, 类型: {}", agentTask.getId(), agentTask.getTaskType());

            // 使用统一的TaskOrchestratorService创建标准化步骤
            List<TaskStep> steps = taskOrchestratorService.createTaskSteps(agentTask);

            log.info("任务步骤创建完成，任务ID: {}, 步骤数量: {}", agentTask.getId(), steps.size());

            // 记录步骤创建历史
            for (TaskStep step : steps) {
                log.debug(
                    "创建步骤 - ID: {}, 名称: {}, 类型: {}, 顺序: {}",
                    step.getId(),
                    step.getStepName(),
                    step.getStepType(),
                    step.getStepOrder()
                );
            }
        } catch (Exception e) {
            log.error("创建任务步骤失败，任务ID: {}, 错误详情: {}", agentTask.getId(), e.getMessage(), e);
            // 不抛出异常，允许任务继续执行，但记录错误
        }
    }

    /**
     * 创建任务上下文
     * <p>
     * 为任务执行创建专门的上下文环境，用于存储任务执行过程中的中间数据、
     * 状态信息和业务参数。上下文在整个任务生命周期中提供数据共享机制。
     *
     * @param agentTask Agent任务实体
     */
    private void createTaskContext(AgentTask agentTask) {
        try {
            log.info("为任务创建执行上下文，任务ID: {}, 类型: {}", agentTask.getId(), agentTask.getTaskType());

            // 创建任务上下文
            AgentContext context = agentContextCoreService.createContext(agentTask.getId(), agentTask.getTaskType());

            // 存储基础任务信息到上下文
            agentContextCoreService.storeVariable(context.getId(), "tenantId", agentTask.getTenantId());
            agentContextCoreService.storeVariable(context.getId(), "taskType", agentTask.getTaskType());
            agentContextCoreService.storeVariable(context.getId(), "title", agentTask.getTitle());
            agentContextCoreService.storeVariable(context.getId(), "description", agentTask.getDescription());
            agentContextCoreService.storeVariable(context.getId(), "priority", agentTask.getPriority());
            agentContextCoreService.storeVariable(context.getId(), "requestData", agentTask.getRequestData());
            agentContextCoreService.storeVariable(context.getId(), "createdBy", agentTask.getCreatedBy());
            agentContextCoreService.storeVariable(context.getId(), "createdAt", agentTask.getCreatedAt().toString());

            // 记录上下文创建历史
            agentContextCoreService.recordHistory(
                context.getId(),
                "CONTEXT_CREATED",
                String.format("任务上下文创建完成 - 任务ID: %d, 类型: %s", agentTask.getId(), agentTask.getTaskType())
            );

            log.info("任务上下文创建完成，任务ID: {}, 上下文ID: {}", agentTask.getId(), context.getId());
        } catch (Exception e) {
            log.error("创建任务上下文失败，任务ID: {}", agentTask.getId(), e);
            // 不抛出异常，允许任务继续执行，但记录错误
        }
    }

    /**
     * 更新任务步骤状态
     * <p>
     * 同步更新与任务关联的所有步骤的状态，确保任务执行过程中的每个步骤都有准确的状态记录。
     * 这对于任务的监控、管理和故障排除至关重要。
     *
     * @param taskId 任务ID
     * @param status 目标状态
     */
    private void updateTaskStepsStatus(Long taskId, TaskStepStatus status) {
        try {
            log.info("更新任务步骤状态，任务ID: {}, 新状态: {}", taskId, status);

            // 获取所有与任务关联的步骤
            List<TaskStep> taskSteps = taskStepCoreService.getTaskStepsByTaskId(taskId);

            // 更新每个步骤的状态
            for (TaskStep step : taskSteps) {
                step.setStatus(status.getValue());
                taskStepCoreService.saveTaskStep(step);

                log.debug("步骤状态更新成功，步骤ID: {}, 新状态: {}", step.getId(), status);
            }

            log.info("所有任务步骤状态更新完成，任务ID: {}", taskId);
        } catch (Exception e) {
            log.error("更新任务步骤状态失败，任务ID: {}, 错误详情: {}", taskId, e.getMessage(), e);
            // 不抛出异常，允许任务继续执行，但记录错误
        }
    }
}
