/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：NullValueFilterUtil.java
 * 包    名：com.whiskerguard.ai.util
 * 描    述：通用null值过滤工具类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/7/1
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.util;

import com.whiskerguard.ai.config.NullValueFilterProperties;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 通用null值过滤工具类
 * <p>
 * 提供统一的null值处理功能，支持将AI服务返回结果中的null值
 * 替换为用户友好的提示文本，避免前端显示空白内容。
 *
 * 主要功能：
 * 1. 字符串null值替换 - 将null字符串替换为"暂无意见"等友好提示
 * 2. 列表null值处理 - 将null列表替换为空列表，列表中的null元素替换为友好提示
 * 3. 对象null值处理 - 递归处理嵌套对象的null字段
 * 4. 配置化替换文本 - 支持通过配置文件自定义替换文本
 * 5. 字段级别配置 - 支持为不同字段配置不同的替换文本
 * 6. 性能优化 - 使用反射缓存机制提高处理性能
 *
 * 使用示例：
 * <pre>
 * // 基本使用
 * ContractReviewResponseDTO filtered = nullValueFilterUtil.filterNullValues(response);
 *
 * // 使用自定义替换文本
 * PolicyGenerationResponse filtered = nullValueFilterUtil.filterNullValues(response, "暂无相关信息");
 * </pre>
 *
 * <AUTHOR> AI Team
 * @since 1.0.0
 */
@Component
public class NullValueFilterUtil {

    private static final Logger log = LoggerFactory.getLogger(NullValueFilterUtil.class);

    /**
     * 反射字段信息缓存
     * 用于提高反射操作的性能，避免重复获取字段信息
     */
    private static final Map<Class<?>, Field[]> FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * 处理过的对象缓存
     * 用于避免循环引用导致的无限递归
     */
    private static final ThreadLocal<Map<Object, Object>> PROCESSED_OBJECTS = ThreadLocal.withInitial(HashMap::new);

    /**
     * null值过滤配置属性
     */
    private final NullValueFilterProperties properties;

    /**
     * 构造函数
     *
     * @param properties null值过滤配置属性
     */
    public NullValueFilterUtil(NullValueFilterProperties properties) {
        this.properties = properties;
    }

    /**
     * 过滤对象中的null值 - 使用默认替换文本
     * <p>
     * 递归遍历对象的所有字段，将null值替换为配置的默认友好提示文本。
     * 支持处理嵌套对象、集合、数组等复杂数据结构。
     *
     * @param <T> 对象类型
     * @param obj 需要过滤的对象
     * @return 过滤后的对象，如果输入为null则返回null
     */
    public <T> T filterNullValues(T obj) {
        return filterNullValues(obj, properties.getDefaultReplacement());
    }

    /**
     * 过滤对象中的null值 - 使用自定义替换文本
     * <p>
     * 递归遍历对象的所有字段，将null值替换为指定的友好提示文本。
     * 支持处理嵌套对象、集合、数组等复杂数据结构。
     *
     * @param <T> 对象类型
     * @param obj 需要过滤的对象
     * @param defaultReplacement 默认替换文本
     * @return 过滤后的对象，如果输入为null则返回null
     */
    public <T> T filterNullValues(T obj, String defaultReplacement) {
        if (obj == null) {
            return null;
        }

        try {
            // 清理线程本地缓存
            PROCESSED_OBJECTS.get().clear();

            // 开始过滤处理
            @SuppressWarnings("unchecked")
            T result = (T) processObject(obj, defaultReplacement);

            log.debug("null值过滤完成，对象类型: {}", obj.getClass().getSimpleName());
            return result;
        } catch (Exception e) {
            log.error("null值过滤处理失败，对象类型: {}, 错误: {}", obj.getClass().getSimpleName(), e.getMessage(), e);
            return obj; // 发生异常时返回原对象
        } finally {
            // 清理线程本地缓存
            PROCESSED_OBJECTS.remove();
        }
    }

    /**
     * 过滤对象中的null值 - 使用字段级别配置
     * <p>
     * 根据字段级别的配置映射，为不同字段使用不同的替换文本。
     * 如果字段没有特定配置，则使用默认替换文本。
     *
     * @param <T> 对象类型
     * @param obj 需要过滤的对象
     * @param fieldReplacements 字段级别的替换文本映射
     * @return 过滤后的对象，如果输入为null则返回null
     */
    public <T> T filterNullValuesWithConfig(T obj, Map<String, String> fieldReplacements) {
        if (obj == null) {
            return null;
        }

        try {
            // 清理线程本地缓存
            PROCESSED_OBJECTS.get().clear();

            // 开始过滤处理
            @SuppressWarnings("unchecked")
            T result = (T) processObjectWithConfig(obj, fieldReplacements);

            log.debug("null值过滤完成（使用字段配置），对象类型: {}", obj.getClass().getSimpleName());
            return result;
        } catch (Exception e) {
            log.error(
                "null值过滤处理失败（使用字段配置），对象类型: {}, 错误: {}",
                obj.getClass().getSimpleName(),
                e.getMessage(),
                e
            );
            return obj; // 发生异常时返回原对象
        } finally {
            // 清理线程本地缓存
            PROCESSED_OBJECTS.remove();
        }
    }

    /**
     * 处理对象 - 使用默认替换文本
     * <p>
     * 递归处理对象的所有字段，将null值替换为指定的默认文本。
     * 支持处理基本类型、字符串、集合、数组和嵌套对象。
     *
     * @param obj 需要处理的对象
     * @param defaultReplacement 默认替换文本
     * @return 处理后的对象
     * @throws Exception 处理过程中的异常
     */
    private Object processObject(Object obj, String defaultReplacement) throws Exception {
        if (obj == null) {
            return null;
        }

        // 检查是否已经处理过此对象（避免循环引用）
        Map<Object, Object> processedMap = PROCESSED_OBJECTS.get();
        if (processedMap.containsKey(obj)) {
            return processedMap.get(obj);
        }

        Class<?> clazz = obj.getClass();

        // 处理基本类型和包装类型
        if (isPrimitiveOrWrapper(clazz)) {
            return obj;
        }

        // 处理字符串类型
        if (obj instanceof String) {
            return obj; // 字符串不为null，直接返回
        }

        // 处理集合类型
        if (obj instanceof Collection) {
            return processCollection((Collection<?>) obj, defaultReplacement);
        }

        // 处理数组类型
        if (clazz.isArray()) {
            return processArray(obj, defaultReplacement);
        }

        // 处理Map类型
        if (obj instanceof Map) {
            return processMap((Map<?, ?>) obj, defaultReplacement);
        }

        // 处理自定义对象
        return processCustomObject(obj, defaultReplacement);
    }

    /**
     * 处理对象 - 使用字段级别配置
     * <p>
     * 递归处理对象的所有字段，根据字段配置使用不同的替换文本。
     *
     * @param obj 需要处理的对象
     * @param fieldReplacements 字段级别的替换文本映射
     * @return 处理后的对象
     * @throws Exception 处理过程中的异常
     */
    private Object processObjectWithConfig(Object obj, Map<String, String> fieldReplacements) throws Exception {
        if (obj == null) {
            return null;
        }

        // 检查是否已经处理过此对象（避免循环引用）
        Map<Object, Object> processedMap = PROCESSED_OBJECTS.get();
        if (processedMap.containsKey(obj)) {
            return processedMap.get(obj);
        }

        Class<?> clazz = obj.getClass();

        // 处理基本类型和包装类型
        if (isPrimitiveOrWrapper(clazz)) {
            return obj;
        }

        // 处理字符串类型
        if (obj instanceof String) {
            return obj; // 字符串不为null，直接返回
        }

        // 处理集合类型
        if (obj instanceof Collection) {
            return processCollectionWithConfig((Collection<?>) obj, fieldReplacements);
        }

        // 处理数组类型
        if (clazz.isArray()) {
            return processArrayWithConfig(obj, fieldReplacements);
        }

        // 处理Map类型
        if (obj instanceof Map) {
            return processMapWithConfig((Map<?, ?>) obj, fieldReplacements);
        }

        // 处理自定义对象
        return processCustomObjectWithConfig(obj, fieldReplacements);
    }

    /**
     * 处理自定义对象
     * <p>
     * 使用反射遍历对象的所有字段，将null值替换为指定文本。
     *
     * @param obj 需要处理的对象
     * @param defaultReplacement 默认替换文本
     * @return 处理后的对象
     * @throws Exception 处理过程中的异常
     */
    private Object processCustomObject(Object obj, String defaultReplacement) throws Exception {
        Class<?> clazz = obj.getClass();

        // 将对象添加到已处理缓存中
        PROCESSED_OBJECTS.get().put(obj, obj);

        // 获取所有字段（包括父类字段）
        Field[] fields = getAllFields(clazz);

        for (Field field : fields) {
            // 跳过静态字段和final字段
            if (Modifier.isStatic(field.getModifiers()) || Modifier.isFinal(field.getModifiers())) {
                continue;
            }

            field.setAccessible(true);
            Object fieldValue = field.get(obj);

            if (fieldValue == null) {
                // 根据字段配置获取替换文本
                String replacementText = getReplacementTextForField(field.getName(), null);
                Object replacement = getReplacementForField(field, replacementText);
                if (replacement != null) {
                    field.set(obj, replacement);
                    log.debug("字段 {} 的null值已替换为: {}", field.getName(), replacement);
                }
            } else {
                // 递归处理非null字段
                Object processedValue = processObject(fieldValue, defaultReplacement);
                if (processedValue != fieldValue) {
                    field.set(obj, processedValue);
                }
            }
        }

        return obj;
    }

    /**
     * 处理自定义对象 - 使用字段级别配置
     * <p>
     * 使用反射遍历对象的所有字段，根据字段配置使用不同的替换文本。
     *
     * @param obj 需要处理的对象
     * @param fieldReplacements 字段级别的替换文本映射
     * @return 处理后的对象
     * @throws Exception 处理过程中的异常
     */
    private Object processCustomObjectWithConfig(Object obj, Map<String, String> fieldReplacements) throws Exception {
        Class<?> clazz = obj.getClass();

        // 将对象添加到已处理缓存中
        PROCESSED_OBJECTS.get().put(obj, obj);

        // 获取所有字段（包括父类字段）
        Field[] fields = getAllFields(clazz);

        for (Field field : fields) {
            // 跳过静态字段和final字段
            if (Modifier.isStatic(field.getModifiers()) || Modifier.isFinal(field.getModifiers())) {
                continue;
            }

            field.setAccessible(true);
            Object fieldValue = field.get(obj);

            if (fieldValue == null) {
                // 根据字段配置获取替换文本
                String replacement = getReplacementTextForField(field.getName(), fieldReplacements);
                Object replacementValue = getReplacementForField(field, replacement);
                if (replacementValue != null) {
                    field.set(obj, replacementValue);
                    log.debug("字段 {} 的null值已替换为: {}", field.getName(), replacementValue);
                }
            } else {
                // 递归处理非null字段
                Object processedValue = processObjectWithConfig(fieldValue, fieldReplacements);
                if (processedValue != fieldValue) {
                    field.set(obj, processedValue);
                }
            }
        }

        return obj;
    }

    /**
     * 处理集合类型
     * <p>
     * 遍历集合中的每个元素，将null元素替换为指定文本，
     * 对非null元素进行递归处理。
     *
     * @param collection 需要处理的集合
     * @param defaultReplacement 默认替换文本
     * @return 处理后的集合
     * @throws Exception 处理过程中的异常
     */
    @SuppressWarnings("unchecked")
    private Collection<Object> processCollection(Collection<?> collection, String defaultReplacement) throws Exception {
        if (collection == null) {
            return new ArrayList<>();
        }

        // 将集合添加到已处理缓存中
        PROCESSED_OBJECTS.get().put(collection, collection);

        Collection<Object> result;
        try {
            // 尝试创建相同类型的集合
            result = (Collection<Object>) collection.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            // 如果无法创建相同类型，使用ArrayList
            result = new ArrayList<>();
        }

        for (Object item : collection) {
            if (item == null) {
                // 将null元素替换为默认文本
                result.add(defaultReplacement);
                log.debug("集合中的null元素已替换为: {}", defaultReplacement);
            } else {
                // 递归处理非null元素
                Object processedItem = processObject(item, defaultReplacement);
                result.add(processedItem);
            }
        }

        return result;
    }

    /**
     * 处理集合类型 - 使用字段级别配置
     *
     * @param collection 需要处理的集合
     * @param fieldReplacements 字段级别的替换文本映射
     * @return 处理后的集合
     * @throws Exception 处理过程中的异常
     */
    @SuppressWarnings("unchecked")
    private Collection<Object> processCollectionWithConfig(Collection<?> collection, Map<String, String> fieldReplacements)
        throws Exception {
        if (collection == null) {
            return new ArrayList<>();
        }

        // 将集合添加到已处理缓存中
        PROCESSED_OBJECTS.get().put(collection, collection);

        Collection<Object> result;
        try {
            // 尝试创建相同类型的集合
            result = (Collection<Object>) collection.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            // 如果无法创建相同类型，使用ArrayList
            result = new ArrayList<>();
        }

        for (Object item : collection) {
            if (item == null) {
                // 将null元素替换为默认文本
                String replacement = properties.getDefaultReplacement();
                result.add(replacement);
                log.debug("集合中的null元素已替换为: {}", replacement);
            } else {
                // 递归处理非null元素
                Object processedItem = processObjectWithConfig(item, fieldReplacements);
                result.add(processedItem);
            }
        }

        return result;
    }

    /**
     * 处理数组类型
     *
     * @param array 需要处理的数组
     * @param defaultReplacement 默认替换文本
     * @return 处理后的数组
     * @throws Exception 处理过程中的异常
     */
    private Object processArray(Object array, String defaultReplacement) throws Exception {
        if (array == null) {
            return null;
        }

        // 将数组添加到已处理缓存中
        PROCESSED_OBJECTS.get().put(array, array);

        Class<?> componentType = array.getClass().getComponentType();
        int length = java.lang.reflect.Array.getLength(array);

        for (int i = 0; i < length; i++) {
            Object item = java.lang.reflect.Array.get(array, i);
            if (item == null && componentType == String.class) {
                // 只对字符串数组的null元素进行替换
                java.lang.reflect.Array.set(array, i, defaultReplacement);
                log.debug("数组索引 {} 的null元素已替换为: {}", i, defaultReplacement);
            } else if (item != null) {
                // 递归处理非null元素
                Object processedItem = processObject(item, defaultReplacement);
                if (processedItem != item) {
                    java.lang.reflect.Array.set(array, i, processedItem);
                }
            }
        }

        return array;
    }

    /**
     * 处理数组类型 - 使用字段级别配置
     *
     * @param array 需要处理的数组
     * @param fieldReplacements 字段级别的替换文本映射
     * @return 处理后的数组
     * @throws Exception 处理过程中的异常
     */
    private Object processArrayWithConfig(Object array, Map<String, String> fieldReplacements) throws Exception {
        if (array == null) {
            return null;
        }

        // 将数组添加到已处理缓存中
        PROCESSED_OBJECTS.get().put(array, array);

        Class<?> componentType = array.getClass().getComponentType();
        int length = java.lang.reflect.Array.getLength(array);

        for (int i = 0; i < length; i++) {
            Object item = java.lang.reflect.Array.get(array, i);
            if (item == null && componentType == String.class) {
                // 只对字符串数组的null元素进行替换
                String replacement = properties.getDefaultReplacement();
                java.lang.reflect.Array.set(array, i, replacement);
                log.debug("数组索引 {} 的null元素已替换为: {}", i, replacement);
            } else if (item != null) {
                // 递归处理非null元素
                Object processedItem = processObjectWithConfig(item, fieldReplacements);
                if (processedItem != item) {
                    java.lang.reflect.Array.set(array, i, processedItem);
                }
            }
        }

        return array;
    }

    /**
     * 处理Map类型
     *
     * @param map 需要处理的Map
     * @param defaultReplacement 默认替换文本
     * @return 处理后的Map
     * @throws Exception 处理过程中的异常
     */
    @SuppressWarnings("unchecked")
    private Map<Object, Object> processMap(Map<?, ?> map, String defaultReplacement) throws Exception {
        if (map == null) {
            return new HashMap<>();
        }

        // 将Map添加到已处理缓存中
        PROCESSED_OBJECTS.get().put(map, map);

        Map<Object, Object> result;
        try {
            // 尝试创建相同类型的Map
            result = (Map<Object, Object>) map.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            // 如果无法创建相同类型，使用HashMap
            result = new HashMap<>();
        }

        for (Map.Entry<?, ?> entry : map.entrySet()) {
            Object key = entry.getKey();
            Object value = entry.getValue();

            // 处理key
            Object processedKey = key == null ? defaultReplacement : processObject(key, defaultReplacement);

            // 处理value
            Object processedValue = value == null ? defaultReplacement : processObject(value, defaultReplacement);

            result.put(processedKey, processedValue);
        }

        return result;
    }

    /**
     * 处理Map类型 - 使用字段级别配置
     *
     * @param map 需要处理的Map
     * @param fieldReplacements 字段级别的替换文本映射
     * @return 处理后的Map
     * @throws Exception 处理过程中的异常
     */
    @SuppressWarnings("unchecked")
    private Map<Object, Object> processMapWithConfig(Map<?, ?> map, Map<String, String> fieldReplacements) throws Exception {
        if (map == null) {
            return new HashMap<>();
        }

        // 将Map添加到已处理缓存中
        PROCESSED_OBJECTS.get().put(map, map);

        Map<Object, Object> result;
        try {
            // 尝试创建相同类型的Map
            result = (Map<Object, Object>) map.getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            // 如果无法创建相同类型，使用HashMap
            result = new HashMap<>();
        }

        String defaultReplacement = properties.getDefaultReplacement();
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            Object key = entry.getKey();
            Object value = entry.getValue();

            // 处理key
            Object processedKey = key == null ? defaultReplacement : processObjectWithConfig(key, fieldReplacements);

            // 处理value
            Object processedValue = value == null ? defaultReplacement : processObjectWithConfig(value, fieldReplacements);

            result.put(processedKey, processedValue);
        }

        return result;
    }

    /**
     * 获取所有字段（包括父类字段）
     * <p>
     * 使用缓存机制提高性能，避免重复反射操作。
     *
     * @param clazz 类对象
     * @return 所有字段数组
     */
    private Field[] getAllFields(Class<?> clazz) {
        return FIELD_CACHE.computeIfAbsent(clazz, this::computeAllFields);
    }

    /**
     * 计算所有字段（包括父类字段）
     *
     * @param clazz 类对象
     * @return 所有字段数组
     */
    private Field[] computeAllFields(Class<?> clazz) {
        List<Field> allFields = new ArrayList<>();
        Class<?> currentClass = clazz;

        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();
            for (Field field : fields) {
                allFields.add(field);
            }
            currentClass = currentClass.getSuperclass();
        }

        return allFields.toArray(new Field[0]);
    }

    /**
     * 根据字段类型获取替换值
     * <p>
     * 根据字段的类型返回合适的替换值：
     * - String类型：返回替换文本
     * - Collection类型：返回空集合
     * - 其他类型：返回null（不进行替换）
     *
     * @param field 字段对象
     * @param replacementText 替换文本
     * @return 替换值
     */
    private Object getReplacementForField(Field field, String replacementText) {
        Class<?> fieldType = field.getType();

        // 字符串类型使用替换文本
        if (fieldType == String.class) {
            return replacementText;
        }

        // 集合类型使用空集合
        if (Collection.class.isAssignableFrom(fieldType)) {
            if (List.class.isAssignableFrom(fieldType)) {
                return new ArrayList<>();
            }
            // 其他集合类型也返回ArrayList
            return new ArrayList<>();
        }

        // Map类型使用空Map
        if (Map.class.isAssignableFrom(fieldType)) {
            return new HashMap<>();
        }

        // 其他类型不进行替换
        return null;
    }

    /**
     * 根据字段名获取替换文本
     * <p>
     * 优先使用字段特定的配置，如果没有则使用默认配置。
     *
     * @param fieldName 字段名
     * @param fieldReplacements 字段级别的替换文本映射
     * @return 替换文本
     */
    private String getReplacementTextForField(String fieldName, Map<String, String> fieldReplacements) {
        // 优先使用字段特定配置
        if (fieldReplacements != null && fieldReplacements.containsKey(fieldName)) {
            return fieldReplacements.get(fieldName);
        }

        // 使用配置文件中的字段特定配置
        Map<String, String> configuredReplacements = properties.getFieldSpecific();
        if (configuredReplacements != null && configuredReplacements.containsKey(fieldName)) {
            return configuredReplacements.get(fieldName);
        }

        // 使用默认配置
        return properties.getDefaultReplacement();
    }

    /**
     * 判断是否为基本类型或包装类型
     * <p>
     * 基本类型和包装类型不需要进行null值处理。
     *
     * @param clazz 类对象
     * @return 如果是基本类型或包装类型则返回true
     */
    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return (
            clazz.isPrimitive() ||
            clazz == Boolean.class ||
            clazz == Byte.class ||
            clazz == Character.class ||
            clazz == Short.class ||
            clazz == Integer.class ||
            clazz == Long.class ||
            clazz == Float.class ||
            clazz == Double.class ||
            clazz == Void.class
        );
    }

    /**
     * 清理缓存
     * <p>
     * 定期清理反射字段缓存，避免内存泄漏。
     * 通常在应用关闭或内存不足时调用。
     */
    public static void clearCache() {
        FIELD_CACHE.clear();
        log.debug("null值过滤工具缓存已清理");
    }

    /**
     * 获取缓存统计信息
     * <p>
     * 返回当前缓存的统计信息，用于监控和调试。
     *
     * @return 缓存统计信息
     */
    public static Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("fieldCacheSize", FIELD_CACHE.size());
        stats.put("cachedClasses", FIELD_CACHE.keySet().stream().map(Class::getSimpleName).toArray());
        return stats;
    }
}
