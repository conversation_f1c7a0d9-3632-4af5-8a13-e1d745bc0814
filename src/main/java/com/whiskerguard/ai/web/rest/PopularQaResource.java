package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.domain.enumeration.PopularQaCategory;
import com.whiskerguard.ai.repository.PopularQaRepository;
import com.whiskerguard.ai.service.PopularQaClickLogService;
import com.whiskerguard.ai.service.PopularQaService;
import com.whiskerguard.ai.service.dto.PopularQaClickLogDTO;
import com.whiskerguard.ai.service.dto.PopularQaDTO;
import com.whiskerguard.ai.web.rest.errors.BadRequestAlertException;
import com.whiskerguard.common.util.TenantContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * AI热门问答管理
 *
 * 该控制器管理猫伯伯智能合规管家系统的热门问答功能，负责处理与{@link com.whiskerguard.ai.domain.PopularQa}实体相关的REST请求。
 *
 * 提供两组API：
 * 1. 前端用户API - 常见问题列表、分类浏览、热门推荐、点击统计和聊天初始化
 * 2. 管理员API - 问答的增删改查、点击统计查看、数据分析报表、批量操作
 *
 * AI Popular Q&A Management Controller
 *
 * REST controller for managing {@link com.whiskerguard.ai.domain.PopularQa} entity in the Whiskerguard Intelligent Compliance Assistant system.
 *
 * Provides two groups of APIs:
 * 1. Frontend User APIs - common questions list, category browsing, trending recommendations, click statistics and chat initialization
 * 2. Admin APIs - CRUD operations for Q&A, click statistics viewing, data analysis reports, batch operations
 */
@RestController
@Tag(name = "PopularQa", description = "AI热门问答API")
public class PopularQaResource {

    private static final Logger log = LoggerFactory.getLogger(PopularQaResource.class);

    private static final String ENTITY_NAME = "popularQa";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PopularQaService popularQaService;
    private final PopularQaRepository popularQaRepository;
    private final PopularQaClickLogService popularQaClickLogService;

    /**
     * 构造函数，通过依赖注入获取所需服务
     *
     * @param popularQaService 热门问答服务
     * @param popularQaRepository 热门问答数据仓库
     * @param popularQaClickLogService 点击日志服务
     *
     * Constructor with dependency injection for required services
     *
     * @param popularQaService Popular Q&A service
     * @param popularQaRepository Popular Q&A repository
     * @param popularQaClickLogService Click log service
     */
    public PopularQaResource(
        PopularQaService popularQaService,
        PopularQaRepository popularQaRepository,
        PopularQaClickLogService popularQaClickLogService
    ) {
        this.popularQaService = popularQaService;
        this.popularQaRepository = popularQaRepository;
        this.popularQaClickLogService = popularQaClickLogService;
    }

    // ==================== 前端用户API ====================

    /**
     * 获取常见问题列表
     *
     * 获取指定租户的常见问题列表，按排序权重和创建时间排序
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 常见问题列表的响应实体
     *
     * Get common questions list
     *
     * Retrieves common questions for a specific tenant, sorted by sort order and creation time
     *
     * @param tenantId Tenant ID
     * @param pageable Pagination parameters
     * @return Response entity with common questions list
     */
    @GetMapping("/api/popular-qa/common")
    @Operation(summary = "获取常见问题列表", description = "获取指定租户的常见问题列表，按排序权重和创建时间排序")
    @ApiResponse(
        responseCode = "200",
        description = "成功获取常见问题列表",
        content = @Content(schema = @Schema(implementation = PopularQaDTO.class))
    )
    public ResponseEntity<List<PopularQaDTO>> getCommonQuestions(
        @Parameter(description = "租户ID", required = false) @RequestParam(required = false) Long tenantId,
        @Parameter(description = "分页参数") Pageable pageable
    ) {
        // 如果未提供tenantId，从上下文获取
        if (tenantId == null) {
            tenantId = TenantContextUtil.getCurrentTenantId();
        }

        log.debug("REST request to get common questions for tenant: {}", tenantId);

        Page<PopularQaDTO> page = popularQaService.findCommonQuestionsByTenant(tenantId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 获取所有问答分类
     *
     * 获取系统中所有可用的问答分类
     *
     * @return 问答分类列表的响应实体
     *
     * Get all question categories
     *
     * Retrieves all available question categories in the system
     *
     * @return Response entity with categories list
     */
    @GetMapping("/api/popular-qa/categories")
    @Operation(summary = "获取所有问答分类", description = "获取系统中所有可用的问答分类")
    public ResponseEntity<List<Map<String, String>>> getAllCategories() {
        log.debug("REST request to get all question categories");

        List<Map<String, String>> categories = new ArrayList<>();
        for (PopularQaCategory category : PopularQaCategory.values()) {
            Map<String, String> map = new HashMap<>();
            map.put("key", category.name());
            map.put("value", category.getValue());
            categories.add(map);
        }

        return ResponseEntity.ok(categories);
    }

    /**
     * 按分类获取问答列表
     *
     * 按照指定分类获取该租户下的问答列表
     *
     * @param category 分类
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 问答列表的响应实体
     *
     * Get questions by category
     *
     * Retrieves questions for a specific tenant filtered by category
     *
     * @param category Category name
     * @param tenantId Tenant ID
     * @param pageable Pagination parameters
     * @return Response entity with questions list
     */
    @GetMapping("/api/popular-qa/by-category/{category}")
    @Operation(summary = "按分类获取问答列表", description = "按照指定分类获取该租户下的问答列表")
    public ResponseEntity<List<PopularQaDTO>> getQuestionsByCategory(
        @Parameter(description = "问答分类", required = true) @PathVariable String category,
        @Parameter(description = "租户ID", required = false) @RequestParam(required = false) Long tenantId,
        @Parameter(description = "分页参数") Pageable pageable
    ) {
        // 如果未提供tenantId，从上下文获取
        if (tenantId == null) {
            tenantId = TenantContextUtil.getCurrentTenantId();
        }

        log.debug("REST request to get questions by category: {} for tenant: {}", category, tenantId);

        try {
            PopularQaCategory qaCategory = PopularQaCategory.valueOf(category.toUpperCase());
            Page<PopularQaDTO> page = popularQaService.findByTenantAndCategory(tenantId, qaCategory, pageable);
            HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
            return ResponseEntity.ok().headers(headers).body(page.getContent());
        } catch (IllegalArgumentException e) {
            log.error("Invalid category: {}", category);
            throw new BadRequestAlertException("无效的分类", ENTITY_NAME, "invalidcategory");
        }
    }

    /**
     * 获取热门推荐问答
     *
     * 获取标记为热门的问答列表
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 热门问答列表的响应实体
     *
     * Get trending questions
     *
     * Retrieves questions marked as trending for a specific tenant
     *
     * @param tenantId Tenant ID
     * @param pageable Pagination parameters
     * @return Response entity with trending questions list
     */
    @GetMapping("/api/popular-qa/trending")
    @Operation(summary = "获取热门推荐问答", description = "获取标记为热门的问答列表")
    public ResponseEntity<List<PopularQaDTO>> getTrendingQuestions(
        @Parameter(description = "租户ID", required = false) @RequestParam(required = false) Long tenantId,
        @Parameter(description = "分页参数") Pageable pageable
    ) {
        // 如果未提供tenantId，从上下文获取
        if (tenantId == null) {
            tenantId = TenantContextUtil.getCurrentTenantId();
        }

        log.debug("REST request to get trending questions for tenant: {}", tenantId);

        Page<PopularQaDTO> page = popularQaService.findTrendingByTenant(tenantId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 点击问答并初始化聊天
     *
     * 记录用户点击，增加问答的点击计数，并初始化聊天会话
     * 同时返回与AI继续对话所需的必要参数
     *
     * @param id 问答ID
     * @param clickLogDTO 点击日志DTO
     * @param request HTTP请求
     * @return 带有会话ID和AI继续对话参数的问答详情的响应实体
     *
     * Click on a question and initialize chat
     *
     * Records user click, increments the click count for the question, and initializes a chat session
     * Also returns necessary parameters for continuing the conversation with AI
     *
     * @param id Question ID
     * @param clickLogDTO Click log data transfer object
     * @param request HTTP request
     * @return Response entity with question details, conversation ID and AI invocation parameters
     */
    @PostMapping("/api/popular-qa/{id}/start-chat")
    @Operation(summary = "点击问答并初始化聊天", description = "记录用户点击，增加问答的点击计数，并初始化聊天会话，返回AI对话所需参数")
    public ResponseEntity<Map<String, Object>> startChatWithQuestion(
        @Parameter(description = "问答ID", required = true) @PathVariable Long id,
        @Parameter(description = "点击日志数据") @RequestBody PopularQaClickLogDTO clickLogDTO,
        HttpServletRequest request
    ) {
        log.debug("REST request to click question and start chat: {}", id);

        // 1. 检索问答详情
        Optional<PopularQaDTO> qaOpt = popularQaService.findOne(id);
        if (qaOpt.isEmpty()) {
            log.error("Question not found: {}", id);
            throw new BadRequestAlertException("问题不存在", ENTITY_NAME, "idnotexists");
        }

        // 2. 增加点击计数
        popularQaService.incrementClickCount(id);

        // 3. 记录点击日志
        if (clickLogDTO != null) {
            // 如果未提供tenantId，从上下文获取
            if (clickLogDTO.getTenantId() == null) {
                clickLogDTO.setTenantId(TenantContextUtil.getCurrentTenantId());
            }

            // 如果未提供employeeId，可以从上下文获取（根据业务需求决定）
            // if (clickLogDTO.getEmployeeId() == null) {
            //     clickLogDTO.setEmployeeId(EmployeeContextUtil.getCurrentEmployeeId());
            // }

            clickLogDTO.setQaId(id);
            clickLogDTO.setClickTime(Instant.now());
            clickLogDTO.setUserIp(request.getRemoteAddr());
            clickLogDTO.setUserAgent(request.getHeader("User-Agent"));

            // 生成会话ID (格式: qa_问答ID_时间戳)
            String conversationId = "qa_" + id + "_" + System.currentTimeMillis();
            clickLogDTO.setConversationId(conversationId);

            // 只有在有足够信息时才保存日志
            if (clickLogDTO.getTenantId() != null && clickLogDTO.getEmployeeId() != null) {
                popularQaClickLogService.save(clickLogDTO);

                // 4. 返回问答详情和会话ID，以及AI继续对话所需的参数
                PopularQaDTO qa = qaOpt.orElseThrow(() -> new BadRequestAlertException("问题不存在", ENTITY_NAME, "idnotexists"));
                Map<String, Object> result = new HashMap<>();

                // 基本问答信息
                result.put("conversationId", conversationId);
                result.put("questionTitle", qa.getQuestionTitle());
                result.put("questionContent", qa.getQuestionContent());
                result.put("answerContent", qa.getAnswerContent());
                result.put("category", qa.getCategory());
                result.put("tags", qa.getTags());

                // 添加AI调用所需参数
                Map<String, Object> aiInvocationParams = new HashMap<>();
                aiInvocationParams.put("toolKey", "kimi"); // 默认使用通用聊天工具
                aiInvocationParams.put("tenantId", clickLogDTO.getTenantId());
                aiInvocationParams.put("employeeId", clickLogDTO.getEmployeeId());

                // 构建初始上下文，包含问题和答案
                StringBuilder contextBuilder = new StringBuilder();
                if (qa.getQuestionContent() != null && !qa.getQuestionContent().trim().isEmpty()) {
                    contextBuilder.append("问题：").append(qa.getQuestionContent()).append("\n\n");
                } else {
                    contextBuilder.append("问题：").append(qa.getQuestionTitle()).append("\n\n");
                }
                contextBuilder.append("回答：").append(qa.getAnswerContent());

                // 元数据包含对话必要信息
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("conversationId", conversationId);
                metadata.put("qaId", id);
                metadata.put("qaCategory", qa.getCategory());
                metadata.put("initialContext", contextBuilder.toString());

                aiInvocationParams.put("metadata", metadata);

                // 将AI调用参数添加到返回结果中
                result.put("aiInvocationParams", aiInvocationParams);

                return ResponseEntity.ok(result);
            } else {
                log.warn("Incomplete click log data (missing tenantId or employeeId), not recording log but returning question details");
                // 返回问答详情，但没有会话ID和AI调用参数
                PopularQaDTO qa = qaOpt.orElseThrow(() -> new BadRequestAlertException("问题不存在", ENTITY_NAME, "idnotexists"));
                Map<String, Object> result = new HashMap<>();
                result.put("questionTitle", qa.getQuestionTitle());
                result.put("questionContent", qa.getQuestionContent());
                result.put("answerContent", qa.getAnswerContent());
                result.put("category", qa.getCategory());
                result.put("tags", qa.getTags());

                return ResponseEntity.ok(result);
            }
        } else {
            log.warn("No click log data provided, not recording");
            // 仍然返回问答详情，但没有会话ID和AI调用参数
            PopularQaDTO qa = qaOpt.orElseThrow(() -> new BadRequestAlertException("问题不存在", ENTITY_NAME, "idnotexists"));
            Map<String, Object> result = new HashMap<>();
            result.put("questionTitle", qa.getQuestionTitle());
            result.put("questionContent", qa.getQuestionContent());
            result.put("answerContent", qa.getAnswerContent());
            result.put("category", qa.getCategory());
            result.put("tags", qa.getTags());

            return ResponseEntity.ok(result);
        }
    }

    // ==================== 管理员API ====================

    /**
     * 创建新的热门问答
     *
     * 管理员创建新的热门问答条目
     *
     * @param popularQaDTO 要创建的热门问答
     * @return 创建成功的热门问答的响应实体
     * @throws URISyntaxException 如果URI语法不正确
     *
     * Create a new popular Q&A
     *
     * Admin creates a new popular Q&A entry
     *
     * @param popularQaDTO The popular Q&A to create
     * @return Response entity with the created popular Q&A
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/api/admin/popular-qa")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(summary = "创建新的热门问答", description = "管理员创建新的热门问答条目")
    @ApiResponse(
        responseCode = "201",
        description = "成功创建热门问答",
        content = @Content(schema = @Schema(implementation = PopularQaDTO.class))
    )
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<PopularQaDTO> createPopularQa(
        @Parameter(description = "热门问答数据", required = true) @Valid @RequestBody PopularQaDTO popularQaDTO
    ) throws URISyntaxException {
        log.debug("REST request to save PopularQa : {}", popularQaDTO);

        if (popularQaDTO.getId() != null) {
            throw new BadRequestAlertException("新创建的问答不能包含ID", ENTITY_NAME, "idexists");
        }

        // 设置默认值
        if (popularQaDTO.getClickCount() == null) {
            popularQaDTO.setClickCount(0L);
        }
        if (popularQaDTO.getIsEnabled() == null) {
            popularQaDTO.setIsEnabled(true);
        }
        if (popularQaDTO.getIsDeleted() == null) {
            popularQaDTO.setIsDeleted(false);
        }
        if (popularQaDTO.getVersion() == null) {
            popularQaDTO.setVersion(1);
        }

        PopularQaDTO result = popularQaService.save(popularQaDTO);
        return ResponseEntity.created(new URI("/api/admin/popular-qa/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * 更新热门问答
     *
     * 管理员更新已有的热门问答条目
     *
     * @param id 要更新的问答ID
     * @param popularQaDTO 更新的问答数据
     * @return 更新后的问答的响应实体
     *
     * Update a popular Q&A
     *
     * Admin updates an existing popular Q&A entry
     *
     * @param id ID of the Q&A to update
     * @param popularQaDTO The updated Q&A data
     * @return Response entity with the updated Q&A
     */
    @PutMapping("/api/admin/popular-qa/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(summary = "更新热门问答", description = "管理员更新已有的热门问答条目")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<PopularQaDTO> updatePopularQa(
        @Parameter(description = "问答ID", required = true) @PathVariable Long id,
        @Parameter(description = "更新的问答数据", required = true) @Valid @RequestBody PopularQaDTO popularQaDTO
    ) {
        log.debug("REST request to update PopularQa : {}", popularQaDTO);

        if (popularQaDTO.getId() == null) {
            throw new BadRequestAlertException("无效ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, popularQaDTO.getId())) {
            throw new BadRequestAlertException("ID不匹配", ENTITY_NAME, "idmismatch");
        }
        if (!popularQaRepository.existsById(id)) {
            throw new BadRequestAlertException("实体不存在", ENTITY_NAME, "idnotfound");
        }

        // 递增版本号
        Integer currentVersion = popularQaDTO.getVersion();
        popularQaDTO.setVersion(currentVersion != null ? currentVersion + 1 : 1);

        PopularQaDTO result = popularQaService.update(popularQaDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, popularQaDTO.getId().toString()))
            .body(result);
    }

    /**
     * 获取所有热门问答（管理员）
     *
     * 管理员获取所有热门问答，包括已禁用的
     *
     * @param pageable 分页参数
     * @return 热门问答列表的响应实体
     *
     * Get all popular Q&As (admin)
     *
     * Admin retrieves all popular Q&As, including disabled ones
     *
     * @param pageable Pagination parameters
     * @return Response entity with popular Q&As list
     */
    @GetMapping("/api/admin/popular-qa")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(summary = "获取所有热门问答", description = "管理员获取所有热门问答，包括已禁用的")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<List<PopularQaDTO>> getAllPopularQas(@Parameter(description = "分页参数") Pageable pageable) {
        log.debug("REST request to get all PopularQas");

        Page<PopularQaDTO> page = popularQaService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 获取指定热门问答详情
     *
     * 管理员获取指定ID的热门问答详细信息
     *
     * @param id 问答ID
     * @return 热门问答详情的响应实体
     *
     * Get a specific popular Q&A details
     *
     * Admin retrieves detailed information for a specific Q&A by ID
     *
     * @param id Question ID
     * @return Response entity with Q&A details
     */
    @GetMapping("/api/admin/popular-qa/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(summary = "获取指定热门问答详情", description = "管理员获取指定ID的热门问答详细信息")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<PopularQaDTO> getPopularQa(@Parameter(description = "问答ID", required = true) @PathVariable Long id) {
        log.debug("REST request to get PopularQa : {}", id);

        Optional<PopularQaDTO> popularQaDTO = popularQaService.findOne(id);
        return ResponseUtil.wrapOrNotFound(popularQaDTO);
    }

    /**
     * 删除热门问答
     *
     * 管理员删除指定ID的热门问答
     *
     * @param id 要删除的问答ID
     * @return 无内容的响应实体
     *
     * Delete a popular Q&A
     *
     * Admin deletes a specific Q&A by ID
     *
     * @param id Question ID to delete
     * @return Response entity with no content
     */
    @DeleteMapping("/api/admin/popular-qa/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @Operation(summary = "删除热门问答", description = "管理员删除指定ID的热门问答")
    @SecurityRequirement(name = "bearerAuth")
    public ResponseEntity<Void> deletePopularQa(@Parameter(description = "问答ID", required = true) @PathVariable Long id) {
        log.debug("REST request to delete PopularQa : {}", id);

        popularQaService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
