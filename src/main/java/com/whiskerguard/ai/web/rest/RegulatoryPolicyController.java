/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RegulatoryPolicyController.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：法规制度转换控制器，负责处理法规到内部管理制度的转换请求
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/6
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.service.compliance.AsyncResultRepository;
import com.whiskerguard.ai.service.compliance.ComplianceVerificationService;
import com.whiskerguard.ai.service.compliance.ComplianceVerificationService.VerificationResult;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.invocation.ModelEnsembleService;
import com.whiskerguard.ai.service.invocation.RagHelper;
import com.whiskerguard.ai.service.invocation.ResultIntegrationEngine;
import com.whiskerguard.ai.util.NullValueFilterUtil;
import com.whiskerguard.ai.web.rest.dto.PolicyGenerationRequest;
import com.whiskerguard.ai.web.rest.dto.PolicyGenerationResponse;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 法规制度转换控制器
 * <p>
 * 负责处理法规到内部管理制度的转换请求，提供API接口进行法规分析和政策生成。
 * 该控制器整合了RAG、模型集成、合规验证和结果整合等功能，支持异步处理大型政策生成任务。
 */
@RestController
@RequestMapping("/api")
public class RegulatoryPolicyController {

    /**
     * 日志记录器
     */
    private final Logger log = LoggerFactory.getLogger(RegulatoryPolicyController.class);

    /**
     * RAG辅助工具，用于检索相关法规知识
     */
    private final RagHelper ragHelper;

    /**
     * 模型集成服务，用于组合多个AI模型的能力
     */
    private final ModelEnsembleService ensembleService;

    /**
     * 合规验证服务，确保生成的政策符合合规要求
     */
    private final ComplianceVerificationService complianceVerificationService;

    /**
     * 结果整合引擎，用于整合多个模型输出
     */
    private final ResultIntegrationEngine integrationEngine;

    /**
     * 异步结果存储库，用于存储长时间运行任务的结果
     */
    private final AsyncResultRepository asyncResultRepository;

    /**
     * null值过滤工具，用于处理AI响应中的null值
     */
    private final NullValueFilterUtil nullValueFilterUtil;

    /**
     * 默认使用的工具Key
     */
    private static final String DEFAULT_TOOL_KEY = "regulatory-policy";

    /**
     * 构造函数，注入所需的服务
     *
     * @param ragHelper RAG辅助类，用于检索相关知识
     * @param ensembleService 模型集成服务，用于组合多个模型能力
     * @param complianceVerificationService 合规验证服务，确保政策合规性
     * @param integrationEngine 结果整合引擎，整合多个模型输出
     * @param asyncResultRepository 异步结果存储库，存储长时间运行任务的结果
     */
    public RegulatoryPolicyController(
        RagHelper ragHelper,
        ModelEnsembleService ensembleService,
        ComplianceVerificationService complianceVerificationService,
        ResultIntegrationEngine integrationEngine,
        AsyncResultRepository asyncResultRepository,
        NullValueFilterUtil nullValueFilterUtil
    ) {
        this.ragHelper = ragHelper;
        this.ensembleService = ensembleService;
        this.complianceVerificationService = complianceVerificationService;
        this.integrationEngine = integrationEngine;
        this.asyncResultRepository = asyncResultRepository;
        this.nullValueFilterUtil = nullValueFilterUtil;
    }

    /**
     * 生成企业内部管理制度
     *
     * @param request 政策生成请求
     * @return 响应实体，包含生成的制度或任务ID（异步模式）
     */
    @PostMapping("/regulatory-policy")
    public ResponseEntity<PolicyGenerationResponse> generatePolicy(@RequestBody PolicyGenerationRequest request) {
        log.info(
            "接收到制度生成请求，法规文本长度: {}，企业: {}",
            request.getLegalText() != null ? request.getLegalText().length() : 0,
            request.getCompanyInfo() != null ? request.getCompanyInfo().getName() : "未指定"
        );

        // 参数验证
        if (request.getLegalText() == null || request.getLegalText().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(PolicyGenerationResponse.failed("法规文本不能为空"));
        }

        if (request.getCompanyInfo() == null || request.getCompanyInfo().getName() == null) {
            return ResponseEntity.badRequest().body(PolicyGenerationResponse.failed("企业信息不能为空"));
        }

        if (request.getEmployeeId() == null) {
            return ResponseEntity.badRequest().body(PolicyGenerationResponse.failed("员工ID不能为空"));
        }

        // 根据isAsync参数决定是同步还是异步处理
        // 如果isAsync为null，则按照原有逻辑判断文本长度
        boolean useAsyncProcessing;
        if (request.getIsAsync() != null) {
            useAsyncProcessing = request.getIsAsync();
            log.debug("根据客户端请求参数决定{}处理", useAsyncProcessing ? "异步" : "同步");
        } else {
            // 向后兼容：根据文本长度判断是同步还是异步处理
            useAsyncProcessing = request.getLegalText().length() > 5000;
            log.debug("根据文本长度决定{}处理 (长度: {})", useAsyncProcessing ? "异步" : "同步", request.getLegalText().length());
        }

        if (useAsyncProcessing) {
            // 异步处理
            String taskId = UUID.randomUUID().toString();
            processPolicyGenerationAsync(request, taskId);

            // 创建响应对象并确保taskId设置正确
            PolicyGenerationResponse inProgressResponse = PolicyGenerationResponse.inProgress(taskId);
            log.info("生成异步任务ID: {}，返回状态: {}", inProgressResponse.getTaskId(), inProgressResponse.getStatus());
            return ResponseEntity.accepted().body(inProgressResponse);
        } else {
            // 同步处理
            try {
                PolicyGenerationResponse response = processPolicyGeneration(request);

                // 过滤响应中的null值，提供友好的用户体验
                PolicyGenerationResponse filteredResponse = nullValueFilterUtil.filterNullValues(response);

                log.info("外规内化处理完成，已过滤null值，任务状态: {}", filteredResponse.getStatus());
                return ResponseEntity.ok(filteredResponse);
            } catch (Exception e) {
                log.error("制度生成过程中发生错误", e);
                return ResponseEntity.internalServerError().body(PolicyGenerationResponse.failed("处理请求时发生错误: " + e.getMessage()));
            }
        }
    }

    /**
     * 同步处理政策生成
     *
     * @param request 政策生成请求
     * @param taskId 可选的任务ID
     * @return 政策生成响应
     */
    private PolicyGenerationResponse processPolicyGeneration(PolicyGenerationRequest request, String taskId) {
        log.debug("开始处理制度生成");

        // 1. 使用RAG增强提示词
        String tenantId = request.getTenantId() != null ? request.getTenantId().toString() : null;
        String enhancedPrompt = ragHelper.enhancePromptForRegulatoryPolicy(request.getLegalText(), request.getCompanyInfo(), tenantId);
        log.debug("增强提示词生成完成，长度: {}", enhancedPrompt.length());

        // 2. 设置要使用的模型列表
        List<String> modelsList;
        if (request.getModelNames() != null && request.getModelNames().length > 0) {
            modelsList = Arrays.asList(request.getModelNames());
        } else {
            // 默认使用三种模型
            modelsList = Arrays.asList("gpt", "deepseek", "kimi");
        }
        log.debug("使用模型: {}", modelsList);

        // 3. 设置整合策略（如果有）
        if (request.getIntegrationStrategy() != null && !request.getIntegrationStrategy().isEmpty()) {
            try {
                ResultIntegrationEngine.IntegrationStrategy strategy = ResultIntegrationEngine.IntegrationStrategy.valueOf(
                    request.getIntegrationStrategy().toUpperCase()
                );
                integrationEngine.setDefaultStrategy(strategy);
                log.debug("使用整合策略: {}", strategy);
            } catch (IllegalArgumentException e) {
                log.warn("无效的整合策略: {}, 使用默认策略", request.getIntegrationStrategy());
            }
        }

        // 4. 调用多模型整合服务
        // 正确创建AiInvocationRequestDTO对象，提供所有必需的参数
        AiInvocationRequestDTO invocationRequest = new AiInvocationRequestDTO(
            DEFAULT_TOOL_KEY, // 工具Key
            enhancedPrompt, // 提示词
            null, // 元数据，当前不需要
            request.getTenantId(), // 租户ID
            request.getEmployeeId() // 员工ID
        );

        String integratedResponse = ensembleService.generateEnsembleResponse(invocationRequest, modelsList);

        log.debug("多模型整合响应生成完成，长度: {}", integratedResponse.length());

        // 5. 验证与审核
        VerificationResult result = complianceVerificationService.verify(
            integratedResponse,
            request.getLegalText(),
            request.getCompanyInfo()
        );
        log.debug("内容验证完成，合规度评分: {}", result.getComplianceScore());

        // 6. 返回结果
        PolicyGenerationResponse response;
        if (taskId != null) {
            response = new PolicyGenerationResponse(
                result.getVerifiedContent(),
                result.getAnnotations(),
                result.getComplianceScore(),
                taskId
            );
            log.debug("生成带taskId的响应: {}", taskId);
        } else {
            response = new PolicyGenerationResponse(result.getVerifiedContent(), result.getAnnotations(), result.getComplianceScore());
        }
        return response;
    }

    /**
     * 同步处理政策生成 - 无任务ID的重载方法
     *
     * @param request 政策生成请求
     * @return 政策生成响应
     */
    private PolicyGenerationResponse processPolicyGeneration(PolicyGenerationRequest request) {
        return processPolicyGeneration(request, null);
    }

    /**
     * 获取异步任务的结果
     *
     * @param taskId 任务ID
     * @return 响应实体，包含任务结果或状态
     */
    @GetMapping("/regulatory-policy/{taskId}")
    public ResponseEntity<PolicyGenerationResponse> getPolicyGenerationResult(@PathVariable String taskId) {
        log.debug("接收到查询制度生成任务结果请求，任务ID: {}", taskId);

        // 检查任务是否存在
        if (!asyncResultRepository.exists(taskId)) {
            log.debug("任务不存在: {}", taskId);
            return ResponseEntity.notFound().build();
        }

        // 获取任务结果
        PolicyGenerationResponse response = asyncResultRepository.findById(taskId);

        // 根据任务状态返回不同的HTTP状态码
        return switch (response.getStatus()) {
            case COMPLETED -> ResponseEntity.ok(response);
            case FAILED -> ResponseEntity.internalServerError().body(response);
            case IN_PROGRESS -> ResponseEntity.accepted().body(response);
            default -> {
                log.warn("未知的任务状态: {}", response.getStatus());
                yield ResponseEntity.internalServerError().body(PolicyGenerationResponse.failed("未知的任务状态"));
            }
        };
    }

    /**
     * 异步处理政策生成
     *
     * @param request 政策生成请求
     * @param taskId 任务ID
     */
    @Async
    public void processPolicyGenerationAsync(PolicyGenerationRequest request, String taskId) {
        log.debug("开始异步处理制度生成，任务ID: {}", taskId);

        try {
            // 设置为处理中状态
            PolicyGenerationResponse inProgressResponse = PolicyGenerationResponse.inProgress(taskId);
            asyncResultRepository.save(taskId, inProgressResponse);

            // 处理逻辑与同步方法相同，直接传递taskId参数
            PolicyGenerationResponse response = processPolicyGeneration(request, taskId);

            // 过滤响应中的null值，提供友好的用户体验
            PolicyGenerationResponse filteredResponse = nullValueFilterUtil.filterNullValues(response);

            // 存储结果以便后续查询
            asyncResultRepository.save(taskId, filteredResponse);

            log.info("异步任务 {} 已完成，已过滤null值，合规度评分: {}", taskId, filteredResponse.getComplianceScore());
        } catch (Exception e) {
            log.error("异步处理制度生成时发生错误，任务ID: {}", taskId, e);

            // 存储错误信息以便后续查询
            asyncResultRepository.saveError(taskId, e.getMessage());
        }
    }
}
