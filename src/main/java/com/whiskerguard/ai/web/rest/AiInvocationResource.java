/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AiInvocationResource.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：AI服务调用控制器，提供AI工具调用的REST API接口
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/5/19
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.service.AiResponseCacheService;
import com.whiskerguard.ai.service.ConversationContextService;
import com.whiskerguard.ai.service.SensitiveWordFilterService;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamResponseDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.service.invocation.AiInvocationServiceCached;
import com.whiskerguard.ai.service.invocation.AiStreamingService;
import com.whiskerguard.common.util.TenantContextUtil;
import jakarta.validation.Valid;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * AI服务对外接口
 * <p>
 * 提供AI工具调用的REST API接口，支持多种AI模型的统一调用入口。
 * 该控制器负责接收客户端的AI调用请求，并将请求转发给相应的AI服务处理。
 * 所有AI调用都会被记录并可追踪，支持多租户隔离。
 */
@RestController
@RequestMapping("/api/ai")
public class AiInvocationResource {

    private static final Logger log = LoggerFactory.getLogger(AiInvocationResource.class);

    /**
     * AI调用服务，负责处理AI工具的实际调用逻辑
     */
    private final AiInvocationService aiInvocationService;

    /**
     * AI流式调用服务，负责处理流式输出
     */
    private final AiStreamingService aiStreamingService;

    /**
     * 缓存增强的AI调用服务，用于高性能场景
     */
    private final AiInvocationServiceCached aiInvocationServiceCached;

    /**
     * 敏感词过滤服务，用于检测和过滤敏感内容
     */
    private final SensitiveWordFilterService sensitiveWordFilterService;

    /**
     * 对话上下文管理服务，用于处理多轮对话的上下文
     */
    private final ConversationContextService conversationContextService;

    /**
     * 构造函数，通过依赖注入初始化AI调用服务
     *
     * @param aiInvocationService AI调用服务实例
     * @param aiStreamingService AI流式调用服务实例
     * @param aiInvocationServiceCached 缓存增强的AI调用服务实例
     * @param sensitiveWordFilterService 敏感词过滤服务实例
     * @param conversationContextService 对话上下文管理服务实例
     */
    public AiInvocationResource(
        AiInvocationService aiInvocationService,
        AiStreamingService aiStreamingService,
        @Autowired(required = false) AiInvocationServiceCached aiInvocationServiceCached,
        SensitiveWordFilterService sensitiveWordFilterService,
        ConversationContextService conversationContextService
    ) {
        this.aiInvocationService = aiInvocationService;
        this.aiStreamingService = aiStreamingService;
        this.aiInvocationServiceCached = aiInvocationServiceCached;
        this.sensitiveWordFilterService = sensitiveWordFilterService;
        this.conversationContextService = conversationContextService;
    }

    /**
     * 调用AI工具接口。
     * <p>
     * POST /api/ai/invoke : 使用指定参数调用AI工具。
     * 该接口接收包含工具类型、提示词和元数据的请求，并返回AI处理结果。
     * 所有调用都会被记录到数据库中，包括请求内容、响应结果和性能指标。
     * 在处理前会进行敏感词检测，如发现敏感内容将拒绝处理。
     *
     * @param dto AI调用请求DTO，包含工具类型(toolKey)、提示词(prompt)和可选的元数据(metadata)
     * @return 包含AI响应结果的ResponseEntity，状态码200(OK)，响应体为AiRequestDTO
     */
    @PostMapping("/invoke")
    public ResponseEntity<AiRequestDTO> invokeAI(@Valid @RequestBody AiInvocationRequestDTO dto) {
        // 从上下文获取tenantId，覆盖请求中的tenantId
        Long currentTenantId = TenantContextUtil.getCurrentTenantId();
        if (currentTenantId != null) {
            dto.setTenantId(currentTenantId);
            log.debug("从租户上下文获取tenantId: {}", currentTenantId);
        }

        // 敏感词检测
        if (dto.getPrompt() != null && !dto.getPrompt().trim().isEmpty()) {
            try {
                boolean containsSensitiveWords = sensitiveWordFilterService.containsSensitiveWords(dto.getPrompt());
                if (containsSensitiveWords) {
                    log.warn("检测到敏感词内容，拒绝处理AI请求。租户ID: {}, 员工ID: {}", dto.getTenantId(), dto.getEmployeeId());
                    // 创建拒绝响应
                    AiRequestDTO rejectedResult = createSensitiveWordRejectionResponse(dto);
                    return ResponseEntity.ok(rejectedResult);
                }
            } catch (Exception e) {
                log.error("敏感词检测失败，继续处理请求: {}", e.getMessage());
                // 敏感词检测失败时，为了不影响正常业务，继续处理请求
            }
        }

        AiRequestDTO result = aiInvocationService.invoke(dto);
        return ResponseEntity.ok(result);
    }

    /**
     * 流式调用AI工具接口。
     * <p>
     * POST /api/ai/stream : 使用指定参数流式调用AI工具。
     * 该接口接收包含工具类型、提示词和元数据的请求，并返回AI处理结果的流。
     * 响应会以Server-Sent Events (SSE)格式返回，前端可以实时显示生成的内容。
     * 所有调用都会被记录到数据库中，包括请求内容、响应结果和性能指标。
     * 在处理前会进行敏感词检测，如发现敏感内容将拒绝处理。
     *
     * @param dto AI流式调用请求DTO，包含工具类型(toolKey)、提示词(prompt)和可选的元数据(metadata)
     * @return 包含AI响应结果流的Flux，媒体类型为TEXT_EVENT_STREAM
     */
    @PostMapping(path = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<AiStreamResponseDTO> streamAI(@Valid @RequestBody AiStreamRequestDTO dto) {
        // 从上下文获取tenantId，覆盖请求中的tenantId
        Long currentTenantId = TenantContextUtil.getCurrentTenantId();
        if (currentTenantId != null) {
            dto.setTenantId(currentTenantId);
            log.debug("从租户上下文获取tenantId: {}", currentTenantId);
        }

        // 敏感词检测
        if (dto.getPrompt() != null && !dto.getPrompt().trim().isEmpty()) {
            try {
                boolean containsSensitiveWords = sensitiveWordFilterService.containsSensitiveWords(dto.getPrompt());
                if (containsSensitiveWords) {
                    log.warn("检测到敏感词内容，拒绝处理AI流式请求。租户ID: {}, 员工ID: {}", dto.getTenantId(), dto.getEmployeeId());

                    // 创建拒绝响应流
                    return Flux.just(createSensitiveWordRejectionStreamResponse(dto));
                }
            } catch (Exception e) {
                log.error("敏感词检测失败，继续处理流式请求: {}", e.getMessage());
                // 敏感词检测失败时，为了不影响正常业务，继续处理请求
            }
        }

        return aiStreamingService.streamInvoke(dto);
    }

    /**
     * 缓存增强的AI工具调用接口（高性能版本）
     * <p>
     * POST /api/ai/invoke/fast : 使用智能缓存的高性能AI调用。
     * 该接口通过多层缓存策略显著提升响应速度，适用于对响应时间要求较高的场景。
     * 相比标准接口，该接口可将响应时间从30-40秒降低到3-8秒。
     *
     * @param dto AI调用请求DTO，包含工具类型(toolKey)、提示词(prompt)和可选的元数据(metadata)
     * @return 包含AI响应结果的ResponseEntity，状态码200(OK)，响应体为AiRequestDTO
     */
    @PostMapping("/invoke/fast")
    public ResponseEntity<AiRequestDTO> invokeAIFast(@Valid @RequestBody AiInvocationRequestDTO dto) {
        if (aiInvocationServiceCached != null) {
            AiRequestDTO result = aiInvocationServiceCached.invoke(dto);
            return ResponseEntity.ok(result);
        } else {
            // 降级到标准服务
            AiRequestDTO result = aiInvocationService.invoke(dto);
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取缓存统计信息
     * <p>
     * GET /api/ai/cache/stats : 获取AI调用缓存的统计信息。
     * 包括命中率、缓存大小、正在进行的请求数量等性能指标。
     *
     * @return 缓存统计信息
     */
    @GetMapping("/cache/stats")
    public ResponseEntity<Object> getCacheStats() {
        if (aiInvocationServiceCached != null) {
            AiResponseCacheService.CacheStatistics stats = aiInvocationServiceCached.getCacheStatistics();
            if (stats != null) {
                return ResponseEntity.ok(stats);
            }
        }
        return ResponseEntity.ok(Map.of("message", "缓存服务未启用"));
    }

    /**
     * 清除AI调用缓存
     * <p>
     * DELETE /api/ai/cache : 手动清除所有AI调用缓存。
     * 用于缓存管理和问题排查。
     *
     * @return 操作结果
     */
    @DeleteMapping("/cache")
    public ResponseEntity<Object> clearCache() {
        if (aiInvocationServiceCached != null) {
            aiInvocationServiceCached.clearCache();
            return ResponseEntity.ok(Map.of("message", "缓存已清空"));
        }
        return ResponseEntity.ok(Map.of("message", "缓存服务未启用"));
    }

    /**
     * 创建敏感词拒绝响应
     *
     * @param dto 原始请求DTO
     * @return 拒绝响应
     */
    private AiRequestDTO createSensitiveWordRejectionResponse(AiInvocationRequestDTO dto) {
        AiRequestDTO rejectedResult = new AiRequestDTO();
        rejectedResult.setId(System.currentTimeMillis()); // 使用时间戳作为临时ID
        rejectedResult.setTenantId(dto.getTenantId());
        rejectedResult.setEmployeeId(dto.getEmployeeId());
        rejectedResult.setToolType(dto.getToolKey());
        rejectedResult.setPrompt(dto.getPrompt());
        rejectedResult.setResponse(
            "很抱歉，您的问题涉及敏感内容，我无法为您提供相关回答。请您重新组织语言，确保内容符合相关规范后再次咨询。感谢您的理解与配合！"
        );
        rejectedResult.setStatus(RequestStatus.FAILED); // 使用枚举值
        rejectedResult.setErrorMessage("检测到敏感内容，请求被拒绝");
        rejectedResult.setVersion(1);
        rejectedResult.setIsDeleted(false);
        rejectedResult.setCreatedAt(Instant.now());
        rejectedResult.setUpdatedAt(Instant.now());
        rejectedResult.setRequestTime(Instant.now());
        rejectedResult.setResponseTime(Instant.now());
        return rejectedResult;
    }

    /**
     * 创建敏感词拒绝流式响应
     *
     * @param dto 原始流式请求DTO
     * @return 拒绝流式响应
     */
    private AiStreamResponseDTO createSensitiveWordRejectionStreamResponse(AiStreamRequestDTO dto) {
        // 根据 AiStreamResponseDTO 的实际字段创建响应
        String rejectionMessage =
            "很抱歉，您的问题涉及敏感内容，我无法为您提供相关回答。请您重新组织语言，确保内容符合相关规范后再次咨询。感谢您的理解与配合！";

        // 使用错误构造函数创建拒绝响应
        AiStreamResponseDTO rejectedResponse = new AiStreamResponseDTO(rejectionMessage);
        return rejectedResponse;
    }

    // ========== 对话上下文管理接口 ==========

    /**
     * 生成新的对话会话ID
     * <p>
     * POST /api/ai/conversations/generate : 生成新的对话会话ID。
     * 用于开始新的多轮对话会话。
     *
     * @return 包含新对话会话ID的ResponseEntity
     */
    @PostMapping("/conversations/generate")
    public ResponseEntity<Map<String, String>> generateConversationId() {
        String conversationId = conversationContextService.generateConversationId();
        log.debug("生成新的对话会话ID: {}", conversationId);

        Map<String, String> response = Map.of("conversationId", conversationId);
        return ResponseEntity.ok(response);
    }

    /**
     * 获取对话历史记录
     * <p>
     * GET /api/ai/conversations/{conversationId} : 获取指定对话的历史记录。
     * 返回该对话会话中的所有AI请求记录，按时间顺序排列。
     *
     * @param conversationId 对话会话ID
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param maxTurns 最大返回轮数（可选，默认10）
     * @return 包含对话历史记录的ResponseEntity
     */
    @GetMapping("/conversations/{conversationId}")
    public ResponseEntity<List<AiRequestDTO>> getConversationHistory(
        @PathVariable String conversationId,
        @RequestParam Long tenantId,
        @RequestParam Long employeeId,
        @RequestParam(defaultValue = "10") Integer maxTurns
    ) {
        log.debug(
            "获取对话历史: conversationId={}, tenantId={}, employeeId={}, maxTurns={}",
            conversationId,
            tenantId,
            employeeId,
            maxTurns
        );

        List<AiRequestDTO> history = conversationContextService.getConversationHistory(conversationId, tenantId, employeeId, maxTurns);

        return ResponseEntity.ok(history);
    }
    /**
     * 获取用户的对话列表
     * <p>
     * GET /api/ai/conversations : 获取指定用户的所有对话会话ID列表。
     * 按创建时间倒序返回，用于展示用户的对话历史。
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @return 包含对话ID列表的ResponseEntity
     */
    //    @GetMapping("/conversations")
    //    public ResponseEntity<List<String>> getUserConversations(
    //        @RequestParam Long tenantId,
    //        @RequestParam Long employeeId
    //    ) {
    //        log.debug("获取用户对话列表: tenantId={}, employeeId={}", tenantId, employeeId);
    //
    //        List<String> conversations = conversationContextService.getUserConversations(tenantId, employeeId);
    //
    //        return ResponseEntity.ok(conversations);
    //    }
}
