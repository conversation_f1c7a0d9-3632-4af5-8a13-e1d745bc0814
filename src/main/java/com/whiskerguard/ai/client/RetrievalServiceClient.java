/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RetrievalServiceClient.java
 * 包    名：com.whiskerguard.ai.client
 * 描    述：RAG检索服务客户端
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client;

import com.whiskerguard.ai.client.dto.RetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;
import com.whiskerguard.ai.client.dto.RegulatoryRetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RegulatoryRetrieveResponseDTO;
import com.whiskerguard.ai.client.dto.BatchRetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.BatchRetrieveResponseDTO;
import com.whiskerguard.ai.client.dto.IndexRequestDTO;
import com.whiskerguard.ai.client.dto.DocumentRecordDTO;
import feign.Request;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * RAG检索服务客户端
 * <p>
 * 该接口使用 Spring Cloud OpenFeign 实现微服务间的远程调用，
 * 用于与RAG检索服务（whiskerguard-retrieval-service）进行通信。
 * 提供向量检索、知识查询等功能。
 *
 * 主要功能：
 * 1. 向量相似度检索
 * 2. 法规专用检索
 * 3. 批量检索
 * 4. 文档索引
 * 5. 多租户数据隔离
 *
 * 配置说明：
 * - 使用 UserFeignClientInterceptor 自动传递认证信息
 * - 配置了自定义的 Feign 配置类，优化超时设置
 *
 * <AUTHOR>
 * @since 1.0
 */
@FeignClient(name = "whiskerguardretrievalservice", configuration = RetrievalServiceClient.RetrievalServiceClientConfiguration.class)
public interface RetrievalServiceClient {

    /**
     * 向量检索 - 根据查询文本检索最相似的文本片段
     *
     * @param tenantId 租户ID
     * @param request 检索请求，包含查询文本、topK、距离度量方式等
     * @return 检索结果
     */
    @PostMapping("/api/retrieve/{tenantId}")
    ResponseEntity<RetrieveResponseDTO> retrieve(
        @PathVariable("tenantId") String tenantId,
        @RequestBody RetrieveRequestDTO request
    );

    /**
     * 法规专用检索 - 执行法规条款、案例、解释和适用性分析检索
     *
     * @param tenantId 租户ID
     * @param request 法规检索请求
     * @return 法规检索结果
     */
    @PostMapping("/api/regulatory-retrieve/{tenantId}")
    ResponseEntity<RegulatoryRetrieveResponseDTO> regulatoryRetrieve(
        @PathVariable("tenantId") String tenantId,
        @RequestBody RegulatoryRetrieveRequestDTO request
    );

    /**
     * 批量检索 - 在单个请求中处理多个检索查询
     *
     * @param tenantId 租户ID
     * @param request 批量检索请求
     * @return 批量检索结果
     */
    @PostMapping("/api/batch-retrieve/{tenantId}")
    ResponseEntity<BatchRetrieveResponseDTO> batchRetrieve(
        @PathVariable("tenantId") String tenantId,
        @RequestBody BatchRetrieveRequestDTO request
    );

    /**
     * 文档索引 - 为租户索引单个文档
     *
     * @param tenantId 租户ID
     * @param request 索引请求，包含文档ID和内容
     * @return 已索引的文档记录
     */
    @PostMapping("/api/index/{tenantId}")
    ResponseEntity<DocumentRecordDTO> indexDocument(
        @PathVariable("tenantId") String tenantId,
        @RequestBody IndexRequestDTO request
    );

    /**
     * 法规检索服务健康检查
     *
     * @param tenantId 租户ID
     * @return 健康状态
     */
    @GetMapping("/api/regulatory-retrieve/{tenantId}/health")
    ResponseEntity<String> regulatoryHealthCheck(@PathVariable("tenantId") String tenantId);

    /**
     * RAG检索服务客户端专用配置类
     *
     * 配置认证拦截器和优化的超时设置
     */
    @Configuration
    class RetrievalServiceClientConfiguration {

        @Bean
        public UserFeignClientInterceptor userFeignClientInterceptor() {
            return new UserFeignClientInterceptor();
        }

        @Bean
        public Request.Options feignRequestOptions() {
            // 设置连接超时和读取超时 - 优化为RAG检索配置
            return new Request.Options(
                2000, // 连接超时 30 秒 - RAG检索可能需要更多时间
                5000 // 读取超时 60 秒 - 向量检索和知识查询可能较耗时
            );
        }
    }
}
