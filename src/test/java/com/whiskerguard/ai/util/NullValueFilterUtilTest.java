/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：NullValueFilterUtilTest.java
 * 包    名：com.whiskerguard.ai.util
 * 描    述：null值过滤工具类测试
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/7/1
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.util;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.config.NullValueFilterProperties;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewResponseDTO;
import com.whiskerguard.ai.web.rest.dto.PolicyGenerationResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * null值过滤工具类测试
 * <p>
 * 测试null值过滤工具的各种功能，包括：
 * 1. 基本null值替换
 * 2. 集合null值处理
 * 3. 嵌套对象处理
 * 4. 字段级别配置
 * 5. 性能测试
 *
 * <AUTHOR> AI Team
 * @since 1.0.0
 */
class NullValueFilterUtilTest {

    private NullValueFilterUtil nullValueFilterUtil;
    private NullValueFilterProperties properties;

    @BeforeEach
    void setUp() {
        properties = new NullValueFilterProperties();
        properties.setDefaultReplacement("暂无意见");
        properties.setEnabled(true);
        properties.setEnableDeepProcessing(true);
        properties.setEnableCollectionProcessing(true);
        properties.setEnableCache(true);

        nullValueFilterUtil = new NullValueFilterUtil(properties);
    }

    @Test
    void testFilterNullValues_BasicStringReplacement() {
        // 创建测试对象
        TestObject testObj = new TestObject();
        testObj.setStringField(null);
        testObj.setNonNullField("保持不变");

        // 执行过滤
        TestObject result = nullValueFilterUtil.filterNullValues(testObj);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getStringField()).isEqualTo("暂无意见");
        assertThat(result.getNonNullField()).isEqualTo("保持不变");
    }

    @Test
    void testFilterNullValues_ListProcessing() {
        // 创建测试对象
        TestObject testObj = new TestObject();
        testObj.setStringList(null);
        testObj.setStringListWithNulls(Arrays.asList("正常值", null, "另一个值"));

        // 执行过滤
        TestObject result = nullValueFilterUtil.filterNullValues(testObj);

        // 验证结果
        assertThat(result.getStringList()).isNotNull().isEmpty();
        assertThat(result.getStringListWithNulls())
            .isNotNull()
            .hasSize(3)
            .containsExactly("正常值", "暂无意见", "另一个值");
    }

    @Test
    void testFilterNullValues_CustomReplacement() {
        // 创建测试对象
        TestObject testObj = new TestObject();
        testObj.setStringField(null);

        // 使用自定义替换文本
        TestObject result = nullValueFilterUtil.filterNullValues(testObj, "自定义替换文本");

        // 验证结果
        assertThat(result.getStringField()).isEqualTo("自定义替换文本");
    }

    @Test
    void testFilterNullValues_FieldSpecificConfig() {
        // 创建测试对象
        TestObject testObj = new TestObject();
        testObj.setStringField(null);
        testObj.setDescription(null);

        // 创建字段特定配置
        Map<String, String> fieldConfig = new HashMap<>();
        fieldConfig.put("stringField", "字段特定替换");
        fieldConfig.put("description", "描述特定替换");

        // 执行过滤
        TestObject result = nullValueFilterUtil.filterNullValuesWithConfig(testObj, fieldConfig);

        // 验证结果
        assertThat(result.getStringField()).isEqualTo("字段特定替换");
        assertThat(result.getDescription()).isEqualTo("描述特定替换");
    }

    @Test
    void testFilterNullValues_ContractReviewResponseDTO() {
        // 创建合同审查响应DTO
        ContractReviewResponseDTO response = new ContractReviewResponseDTO();
        response.setReviewId(1L);
        response.setRiskSummary(null);
        response.setRecommendations(null);
        response.setAiModelInfo(null);

        // 执行过滤
        ContractReviewResponseDTO result = nullValueFilterUtil.filterNullValues(response);

        // 验证结果
        assertThat(result.getReviewId()).isEqualTo(1L);
        assertThat(result.getRiskSummary()).isEqualTo("暂无风险总结");
        assertThat(result.getRecommendations()).isNotNull().isEmpty();
        assertThat(result.getAiModelInfo()).isEqualTo("模型信息不可用");
    }

    @Test
    void testFilterNullValues_PolicyGenerationResponse() {
        // 创建政策生成响应
        PolicyGenerationResponse response = new PolicyGenerationResponse();
        response.setContent(null);
        response.setAnnotations(null);

        // 执行过滤
        PolicyGenerationResponse result = nullValueFilterUtil.filterNullValues(response);

        // 验证结果
        assertThat(result.getContent()).isEqualTo("暂无内容");
        assertThat(result.getAnnotations()).isNotNull().isEmpty();
    }

    @Test
    void testFilterNullValues_InternalPolicyReviewResponseDTO() {
        // 创建内部制度审查响应DTO
        InternalPolicyReviewResponseDTO response = new InternalPolicyReviewResponseDTO();
        response.setReviewId(1L);
        response.setRiskSummary(null);
        response.setOverallRecommendations(null);
        response.setExecutiveSummary(null);

        // 执行过滤
        InternalPolicyReviewResponseDTO result = nullValueFilterUtil.filterNullValues(response);

        // 验证结果
        assertThat(result.getReviewId()).isEqualTo(1L);
        assertThat(result.getRiskSummary()).isEqualTo("暂无风险总结");
        assertThat(result.getOverallRecommendations()).isNotNull().isEmpty();
        assertThat(result.getExecutiveSummary()).isEqualTo("暂无摘要");
    }

    @Test
    void testFilterNullValues_NullInput() {
        // 测试null输入
        TestObject result = nullValueFilterUtil.filterNullValues(null);
        assertThat(result).isNull();
    }

    @Test
    void testCacheStatistics() {
        // 创建测试对象并过滤
        TestObject testObj = new TestObject();
        nullValueFilterUtil.filterNullValues(testObj);

        // 获取缓存统计
        Map<String, Object> stats = NullValueFilterUtil.getCacheStatistics();
        assertThat(stats).isNotNull();
        assertThat(stats.get("fieldCacheSize")).isNotNull();
    }

    @Test
    void testClearCache() {
        // 创建测试对象并过滤（填充缓存）
        TestObject testObj = new TestObject();
        nullValueFilterUtil.filterNullValues(testObj);

        // 清理缓存
        NullValueFilterUtil.clearCache();

        // 验证缓存已清理
        Map<String, Object> stats = NullValueFilterUtil.getCacheStatistics();
        assertThat((Integer) stats.get("fieldCacheSize")).isEqualTo(0);
    }

    /**
     * 测试用的简单对象
     */
    static class TestObject {

        private String stringField;
        private String nonNullField;
        private String description;
        private List<String> stringList;
        private List<String> stringListWithNulls;
        private Integer integerField;

        // Getters and Setters
        public String getStringField() {
            return stringField;
        }

        public void setStringField(String stringField) {
            this.stringField = stringField;
        }

        public String getNonNullField() {
            return nonNullField;
        }

        public void setNonNullField(String nonNullField) {
            this.nonNullField = nonNullField;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public List<String> getStringList() {
            return stringList;
        }

        public void setStringList(List<String> stringList) {
            this.stringList = stringList;
        }

        public List<String> getStringListWithNulls() {
            return stringListWithNulls;
        }

        public void setStringListWithNulls(List<String> stringListWithNulls) {
            this.stringListWithNulls = stringListWithNulls;
        }

        public Integer getIntegerField() {
            return integerField;
        }

        public void setIntegerField(Integer integerField) {
            this.integerField = integerField;
        }
    }
}
